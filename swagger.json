{"openapi": "3.0.1", "info": {"title": "ALOA API", "description": "IoT API Docs"}, "servers": [{"url": "https://aloa-dev.logisteq.com", "description": "Development"}, {"url": "http://localhost:6821", "description": "Localhost-NoAuth"}], "security": [{"JWT Authentication": []}], "tags": [{"name": "기사"}, {"name": "차량"}, {"name": "부서"}, {"name": "배송"}, {"name": "인증키 V2"}, {"name": "프로젝트"}, {"name": "허브"}, {"name": "배송 경로"}], "paths": {"/api/deliveries/{deliveryId}/status": {"get": {"tags": ["배송"], "summary": "배송상태 조회", "description": "배송상태를 조회한다.", "operationId": "getDeliveryFailureOrCompletedHistory", "parameters": [{"name": "deliveryId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "riderId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"type": "object"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"type": "object"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "204": {"description": "No Content", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "object"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DeliveryStatusDTO"}}}}}}, "put": {"tags": ["배송"], "summary": "배송상태 변경", "description": "배송상태를 변경한다.", "operationId": "updateDeliveryStatus", "parameters": [{"name": "deliveryId", "in": "path", "description": "배송 아이디", "required": true, "schema": {"minimum": 1, "type": "integer", "format": "int64"}}, {"name": "riderId", "in": "query", "description": "기사 아이디", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "projectId", "description": "프로젝트 아이디", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeliveryStatusDTO"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"type": "object"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"type": "object"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "204": {"description": "No Content", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "object"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK"}}}}, "/api/deliveries/{deliveryId}/message": {"put": {"tags": ["배송"], "summary": "배송완료/실패 메시지 변경", "description": "배송완료/실패 메시지를 변경한다.", "operationId": "updateDeliveryMessage", "parameters": [{"name": "deliveryId", "in": "path", "required": true, "schema": {"minimum": 1, "type": "integer", "format": "int64"}}, {"name": "riderId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeliveryStatusDTO"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"type": "object"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"type": "object"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "204": {"description": "No Content", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "object"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK"}}}}, "/api/deliveries/route": {"put": {"tags": ["배송 경로"], "summary": "배송 순서 및 경로선형 업데이트", "description": "배송 순서 및 경로선형을 업데이트한다.", "operationId": "updateDeliveryRoutePath", "parameters": [{"name": "riderId", "in": "query", "description": "기사 아이디", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "projectId", "in": "query", "description": "프로젝트 아이디", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeliveryRoutesRequestDTO"}}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"type": "object"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"type": "object"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "204": {"description": "No Content", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "object"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK"}}}}, "/api/deliveries/routesection": {"put": {"tags": ["배송 경로"], "summary": "구간 경로선형 업데이트", "description": "배송출발 시와 이동 중 경로재탐색 시 구간 경로선형을 업데이트한다.", "operationId": "updateDeliveryRouteSection", "parameters": [{"name": "riderId", "in": "query", "description": "기사 아이디", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "projectId", "in": "query", "description": "프로젝트 아이디", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "isPartial", "in": "query", "description": "부분 경로 여부(배송출발 시에는 false, 경로재탐색 시에는 true)", "required": true, "schema": {"type": "boolean"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeliveryRoutesRequestDTO"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"type": "object"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"type": "object"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "204": {"description": "No Content", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "object"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK"}}}}, "/api/deliveries/deliveries-bundle-ended": {"put": {"tags": ["배송"], "summary": "묶음배송완료", "description": "묶음배송완료 처리한다.", "operationId": "setDeliveriesBundleEnded", "parameters": [{"name": "projectId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "riderId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeliveryBundleEndedDTO"}}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"type": "object"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"type": "object"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "204": {"description": "No Content", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "object"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK"}}}}, "/api/deliveries/cluster": {"put": {"tags": ["배송"], "summary": "기사에게 배차", "description": "배송지를 특정 기사에게 배차한다.", "operationId": "changeClusterDeliveriesToRider", "parameters": [{"name": "deliveryIds", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "qrBarCode", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "userInfo", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/WebUserDetails"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeliveryRiderOrderDTO"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"type": "object"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"type": "object"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "204": {"description": "No Content", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "object"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeliveryDTO"}}}}}}}}, "/api/deliveries/cancel-going": {"put": {"tags": ["배송"], "summary": "배송출발 취소", "description": "배송중인 경우 취소한다.", "operationId": "resetDeliveryStatusToReady", "parameters": [{"name": "deliveryId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"type": "object"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"type": "object"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "204": {"description": "No Content", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "object"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK"}}}}, "/api/deliveries/{deliveryId}/memo": {"post": {"tags": ["배송"], "operationId": "saveDeliveryMemo", "parameters": [{"name": "deliveryId", "in": "path", "required": true, "schema": {"minimum": 1, "type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeliveryMemoDTO"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"type": "object"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"type": "object"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "204": {"description": "No Content", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "object"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK"}}}}, "/api/deliveries/{deliveryId}/file": {"get": {"tags": ["배송"], "summary": "배송이미지 조회", "description": "배송완료/실패 사진을 조회한다.", "operationId": "getUploadFile", "parameters": [{"name": "deliveryId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "projectId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "riderId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "fileCategory", "in": "query", "required": true, "schema": {"type": "string", "enum": ["RIDER_PROFILE", "DELIVERY_PRODUCT", "DELIVERY_COMPLETE", "WEB_USER_PROFILE"]}}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"type": "object"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"type": "object"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "204": {"description": "No Content", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "object"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FileDTO"}}}}}}}, "post": {"tags": ["배송"], "summary": "배송이미지 업로드", "description": "배송완료/실패 사진을 업로드한다.", "operationId": "uploadFile", "parameters": [{"name": "deliveryId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "riderId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "projectId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "fileCategory", "in": "query", "required": true, "schema": {"type": "string", "enum": ["RIDER_PROFILE", "DELIVERY_PRODUCT", "DELIVERY_COMPLETE", "WEB_USER_PROFILE"]}}, {"name": "prefix", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "files", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string", "format": "binary"}}}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"type": "object"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"type": "object"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "204": {"description": "No Content", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "object"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"type": "object"}}}}, "412": {"description": "Precondition Failed", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FileDTO"}}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FileDTO"}}}}}}}}, "/api/deliveries/delivery-ended": {"post": {"tags": ["배송"], "summary": "단일 강제배송완료/실패", "description": "단일 배송을 강제로 배송완료/실패 처리한다.", "operationId": "setDeliveryEnded", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeliveryEndedDTO"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"type": "object"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"type": "object"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "204": {"description": "No Content", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "object"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK"}}}}, "/api/deliveries/deliveries-ended": {"post": {"tags": ["배송"], "summary": "다중 강제배송완료/실패", "description": "여러 배송을 강제로 배송완료/실패 처리한다.", "operationId": "setDeliveriesEnded", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeliveryEndedDTO"}}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"type": "object"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"type": "object"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "204": {"description": "No Content", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "object"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK"}}}}, "/api/auth/v2/key": {"post": {"tags": ["인증키 V2"], "summary": "인증키 발송", "description": "인증키를 발송한다.", "operationId": "send<PERSON><PERSON><PERSON><PERSON>", "parameters": [{"name": "userType", "in": "query", "required": false, "schema": {"type": "string", "enum": ["WEB", "RIDER", "CALL"], "default": "RIDER"}}, {"name": "appId", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64", "default": 1}}, {"name": "withAppUrl", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthSendData"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"type": "object"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"type": "object"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "204": {"description": "No Content", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "object"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK"}}}}, "/api/auth/v2/key/verification": {"post": {"tags": ["인증키 V2"], "summary": "인증키 검증", "description": "인증키를 검증한다.", "operationId": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parameters": [{"name": "userType", "in": "query", "required": false, "schema": {"type": "string", "enum": ["WEB", "RIDER", "CALL"], "default": "RIDER"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthKeyData"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"type": "object"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"type": "object"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "204": {"description": "No Content", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "object"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"type": "object"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}}, "/api/vehicle/{vehicleId}": {"get": {"tags": ["차량"], "summary": "기사의 차량정보 조회", "description": "기사의 차량정보를 조회한다.", "operationId": "getVehicle", "parameters": [{"name": "vehicleId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"type": "object"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"type": "object"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "204": {"description": "No Content", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "object"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/VehicleDTO"}}}}}}}, "/api/riders/{riderId}": {"get": {"tags": ["기사"], "summary": "기사정보 조회", "description": "기사정보를 조회한다.", "operationId": "getRider", "parameters": [{"name": "riderId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "projectId", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"type": "object"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"type": "object"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "204": {"description": "No Content", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "object"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RiderDTO"}}}}}}}, "/api/riders/{riderId}/message": {"get": {"tags": ["기사"], "operationId": "getRiderMessage", "parameters": [{"name": "riderId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "userId", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "projectId", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"type": "object"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"type": "object"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "204": {"description": "No Content", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "object"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RiderMessageDTO"}}}}}}}, "/api/projects/auto-add-rider": {"get": {"tags": ["프로젝트"], "operationId": "addAutoRegionRiderToProject", "parameters": [{"name": "userId", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "projectId", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"type": "object"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"type": "object"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "204": {"description": "No Content", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "object"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RiderDTO"}}}}}}}}, "/api/projects": {"get": {"tags": ["프로젝트"], "summary": "기사의 프로젝트 목록 조회", "description": "기사의 프로젝트 목록을 조회한다.", "operationId": "getProjectListByRiderId", "parameters": [{"name": "riderId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"type": "object"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"type": "object"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "204": {"description": "No Content", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "object"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RiderProjectDTO"}}}}}}}}, "/api/hubs": {"get": {"tags": ["허브"], "summary": "Hub 목록 조회", "description": "조직 코드명으로 Hub 목록을 조회한다.", "operationId": "getHubListByCodeName", "parameters": [{"name": "codeName", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"type": "object"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"type": "object"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "204": {"description": "No Content", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "object"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/HubDTO"}}}}}}}}, "/api/departments": {"get": {"tags": ["부서"], "summary": "기사의 부서 목록 조회", "description": "기사의 부서 목록을 조회한다. (level: 3-지점, 4-TC)", "operationId": "getLevelDepartmentList", "parameters": [{"name": "authClientDetails", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/AuthClientDetails"}}, {"name": "organizationId", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "level", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"type": "object"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"type": "object"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "204": {"description": "No Content", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "object"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DepartmentDTO"}}}}}}}}, "/api/deliveries/command/oliveyoung/create": {"get": {"tags": ["배송"], "operationId": "createAutoOliveYoungProject", "parameters": [{"name": "date", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"type": "object"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"type": "object"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "204": {"description": "No Content", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "object"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK"}}}}, "/api/deliveries": {"get": {"tags": ["배송"], "summary": "배송목록 조회 (with Paging)", "description": "배송목록을 조회한다.", "operationId": "getDeliveriesWithPaging", "parameters": [{"name": "userId", "in": "query", "required": false, "schema": {"minimum": 1, "type": "integer", "format": "int64"}}, {"name": "riderId", "in": "query", "required": false, "schema": {"minimum": 1, "type": "integer", "format": "int64"}}, {"name": "projectId", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "pageable", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PageRequestDTO"}}, {"name": "dateFrom", "in": "query", "required": false, "schema": {"type": "string", "format": "date-time"}}, {"name": "dateTo", "in": "query", "required": false, "schema": {"type": "string", "format": "date-time"}}, {"name": "deliveryStatusList", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["배차대기", "배송전", "배송중", "서비스중", "거절", "배송실패", "배송완료", "미배송"]}}}, {"name": "type", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "deliveryIds", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"type": "object"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"type": "object"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"type": "object"}}}}, "204": {"description": "No Content", "content": {"*/*": {"schema": {"type": "object"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "object"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"type": "object"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageDeliveryDTO"}}}}}}}}, "components": {"schemas": {"AddressDTO": {"type": "object", "properties": {"id": {"type": "integer", "description": "주소 ID", "format": "int64"}, "base": {"type": "string", "description": "기본주소"}, "detail": {"type": "string", "description": "상세주소"}, "zipCode": {"type": "string", "description": "우편번호"}, "location": {"$ref": "#/components/schemas/CoordinateDTO"}, "entrance": {"$ref": "#/components/schemas/CoordinateDTO"}, "entranceVehicle": {"$ref": "#/components/schemas/CoordinateDTO"}, "areaPolygon": {"type": "array", "description": "폴리곤 정보", "items": {"$ref": "#/components/schemas/CoordinateDTO"}}, "locality": {"type": "string", "description": "동", "example": "상일동"}, "subAdminArea": {"type": "string", "description": "시군구", "example": "종로구"}, "eupMyeonDong": {"type": "string", "description": "읍면동", "example": "종로1가"}}, "description": "주소 정보"}, "AuthClientDetails": {"type": "object", "properties": {"scope": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "exp": {"type": "integer", "format": "int64"}, "authorities": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "jti": {"type": "string"}, "client_id": {"type": "string"}, "user_name": {"type": "string"}}}, "AuthKeyData": {"required": ["auth<PERSON><PERSON>", "mobile"], "type": "object", "properties": {"authKey": {"type": "string", "description": "인증키", "example": "10527"}, "mobile": {"type": "string", "description": "전화번호", "example": "010112345678"}, "email": {"type": "string", "description": "이메일", "example": "<EMAIL>"}}}, "AuthSendData": {"required": ["mobile"], "type": "object", "properties": {"mobile": {"type": "string", "description": "전화번호", "example": "01012345678"}}}, "CoordinateDTO": {"required": ["x", "y"], "type": "object", "properties": {"x": {"minimum": 0, "type": "number", "format": "double"}, "y": {"minimum": 0, "type": "number", "format": "double"}, "z": {"minimum": 0, "type": "number", "format": "double"}}, "description": "실제 다중경로 목록"}, "DeliveryAllocationDTO": {"type": "object", "properties": {"id": {"type": "integer", "description": "배송할당 ID", "format": "int64", "example": 1532339}, "deliveryId": {"type": "integer", "description": "배송 ID", "format": "int64", "example": 1411651}, "projectId": {"type": "integer", "description": "프로젝트 ID", "format": "int64", "example": 13342}, "riderId": {"type": "integer", "description": "기사 ID", "format": "int64", "example": 6433}, "orderNum": {"type": "integer", "description": "배송순서", "format": "int32", "example": 1}, "boxNum": {"type": "integer", "description": "박스번호", "format": "int32", "example": 123}, "realDistance": {"type": "number", "description": "실제 이동거리(m)", "format": "double", "example": 46349.7706334733}, "realTime": {"type": "integer", "description": "실제 소요시간(sec)", "format": "int64", "example": 2580}, "routePath": {"type": "array", "description": "경로", "items": {"$ref": "#/components/schemas/CoordinateDTO"}}, "predictionDistance": {"type": "number", "description": "예상 이동거리(m)", "format": "double", "example": 50.0088315693237}, "predictionTime": {"type": "integer", "description": "예상 소요시간(sec)", "format": "int64", "example": 30}, "routePathReal": {"type": "array", "description": "실제 다중경로", "items": {"$ref": "#/components/schemas/CoordinateDTO"}}, "etaDateTimeReal": {"type": "string", "description": "실제 예상도착일시", "format": "date-time"}, "estimatedMetersReal": {"type": "number", "description": "실제 이동거리(m)", "format": "double", "example": 24.3156932372912}, "estimatedSecondsReal": {"type": "integer", "description": "실제 소요시간(sec)", "format": "int64", "example": 30}, "status": {"type": "string", "description": "배송 상태", "example": "READY", "enum": ["배차대기", "배송전", "배송중", "서비스중", "거절", "배송실패", "배송완료", "미배송"]}, "etaDateTime": {"type": "string", "description": "도착예정일시", "format": "date-time"}, "fileCountForCompleted": {"type": "integer", "description": "배송이미지 갯수", "format": "int64", "example": 1}, "inspectionStatus": {"type": "string", "description": "검수상태", "example": "PICKUP", "enum": ["미검수", "상차검수", "하차검수", "해당없음"]}, "isInspectionEnded": {"type": "boolean", "description": "검수마감 여부", "example": false}, "pickupVerificationStatus": {"type": "string", "description": "분류검증상태", "example": "VERIFIED", "enum": ["미검증", "검증", "해당없음"]}, "createAt": {"type": "string", "description": "배송할당 생성일시", "format": "date-time"}, "updateAt": {"type": "string", "description": "배송할당 변경일시", "format": "date-time"}}, "description": "배송할당 목록"}, "DeliveryBundleEndedDTO": {"required": ["deliveryId", "deliveryStatus"], "type": "object", "properties": {"deliveryId": {"type": "integer", "description": "배송 ID", "format": "int64", "example": 1411651}, "isForced": {"type": "boolean", "description": "강제여부", "example": true}, "deliveryStatus": {"type": "string", "description": "배송 상태", "example": "COMPLETED", "enum": ["배차대기", "배송전", "배송중", "서비스중", "거절", "배송실패", "배송완료", "미배송"]}, "deliveryCompletedType": {"type": "string", "description": "배송완료 유형", "example": "NORMAL_DELIVERY", "enum": ["NORMAL_DELIVERY", "CORRESPONDENCE", "COMMON_ENTRANCE", "SECURITY_OFFICE", "OTHER", "RETRIEVAL", "TRANSFER_DELIVERY", "NORMAL_DELIVERY_CONTACT", "DOOR", "STORAGE_BOX"]}, "deliveryCompletedMessage": {"type": "string", "description": "배송완료 메시지", "example": "3층문앞에 두었습니다 확인바랍니다"}, "deliveryFailureType": {"type": "string", "description": "배송실패 유형", "example": "APPOINTED_DATE", "enum": ["NOBODY_HOME", "WRONG_ADDR", "LOST_ITEM", "BROKEN_ITEM", "OTHER", "MISDELIVERY", "TRANSFER_ITEM", "ORDER_CANCELLATION", "DISASTER", "CUST_REFUSE", "APPOINTED_DATE", "DELAYED_DELIVERY", "UNVISIT", "POSTPONEMENT", "DELIVERY_CANCELLATION", "QR_VERIFICATION_FAIL"]}, "deliveryFailureMessage": {"type": "string", "description": "배송실패 메시지", "example": "영업사원이 이미 수거해 감"}, "validDelivery": {"type": "boolean"}}}, "DeliveryDTO": {"type": "object", "properties": {"deliveryId": {"type": "integer", "description": "배송 ID", "format": "int64", "example": 1411651}, "receiverName": {"type": "string", "description": "방문지명", "example": "서울시청"}, "receiverPhoneNumber": {"type": "string", "description": "수신인 전화번호"}, "receiverRealPhoneNumber": {"type": "string", "description": "수신인 실전화번호"}, "receiverOwner": {"type": "string", "description": "수신인"}, "productName": {"type": "string", "description": "물품명", "example": "테이퍼드 크롭 데님 팬츠"}, "productWidth": {"type": "integer", "description": "물품 너비", "format": "int32", "example": 10}, "productLength": {"type": "integer", "description": "물품 길이", "format": "int32", "example": 10}, "productHeight": {"type": "integer", "description": "물품 높이", "format": "int32", "example": 10}, "productWeight": {"type": "number", "description": "물품 무게", "format": "float", "example": 3.14}, "productSize": {"type": "string", "description": "물품 크기", "example": "중형", "enum": ["극소", "소형", "중형", "대형", "특대", "이형물"]}, "productQuantity": {"type": "integer", "description": "물품 수량", "format": "int32", "example": 1}, "destinationAddress": {"$ref": "#/components/schemas/AddressDTO"}, "visitReservationDateTime": {"type": "string", "description": "방문예약일시", "format": "date-time"}, "deliveryStartTime": {"$ref": "#/components/schemas/LocalTime"}, "deliveryEndTime": {"$ref": "#/components/schemas/LocalTime"}, "pickUpTime": {"$ref": "#/components/schemas/LocalTime"}, "dropOffTime": {"$ref": "#/components/schemas/LocalTime"}, "duration": {"type": "integer", "description": "체류시간(min)", "format": "int64", "example": 3}, "deliveryMessage": {"type": "string", "description": "배송 메시지", "example": "파손주의"}, "deliveryStatus": {"type": "string", "description": "배송 상태", "example": "READY", "enum": ["배차대기", "배송전", "배송중", "서비스중", "거절", "배송실패", "배송완료", "미배송"]}, "deliveryType": {"type": "string", "description": "배송 유형", "example": "방문지", "enum": ["물류센터", "물류거점", "방문지", "회차지점"]}, "visitType": {"type": "string", "description": "업무 유형", "example": "배송", "enum": ["상차", "하차", "프리미엄", "배송", "회수", "수거", "교환"]}, "deliveryCompletedType": {"type": "string", "description": "배송완료 유형", "example": "NORMAL_DELIVERY", "enum": ["NORMAL_DELIVERY", "CORRESPONDENCE", "COMMON_ENTRANCE", "SECURITY_OFFICE", "OTHER", "RETRIEVAL", "TRANSFER_DELIVERY", "NORMAL_DELIVERY_CONTACT", "DOOR", "STORAGE_BOX"]}, "deliveryCompletedMessage": {"type": "string", "description": "배송완료 메시지", "example": "3층문앞에 두었습니다 확인바랍니다"}, "deliveryFailureType": {"type": "string", "description": "배송실패 유형", "example": "WRONG_ADDR", "enum": ["NOBODY_HOME", "WRONG_ADDR", "LOST_ITEM", "BROKEN_ITEM", "OTHER", "MISDELIVERY", "TRANSFER_ITEM", "ORDER_CANCELLATION", "DISASTER", "CUST_REFUSE", "APPOINTED_DATE", "DELAYED_DELIVERY", "UNVISIT", "POSTPONEMENT", "DELIVERY_CANCELLATION", "QR_VERIFICATION_FAIL"]}, "deliveryFailureMessage": {"type": "string", "description": "배송실패 메시지", "example": "영업사원이 이미 수거해 감"}, "customerOrderId": {"type": "string", "description": "고객사에서 관리하는 주문 ID", "example": "3710000055812"}, "trackingNumber": {"type": "string", "description": "운송장 번호", "example": "3710000055812"}, "clientId": {"type": "string", "description": "고객 ID", "example": "A01"}, "customerProductImageUrls": {"type": "array", "description": "상품이미지 URL 목록", "example": "['https://cdn.pixabay.com/photo/2021/01/01/13/21/guggenheim-museum-2707258_1280.jpg']", "items": {"type": "string", "description": "상품이미지 URL 목록", "example": "['https://cdn.pixabay.com/photo/2021/01/01/13/21/guggenheim-museum-2707258_1280.jpg']"}}, "deliveryRequestAt": {"type": "string", "description": "배송요청일시", "format": "date-time"}, "deliveryDoneAt": {"type": "string", "description": "배송완료일시", "format": "date-time"}, "riderId": {"type": "integer", "description": "기사 ID", "format": "int64", "example": 6433}, "groupName": {"type": "string", "description": "권역명", "example": "강남1"}, "orderNum": {"type": "integer", "description": "배송순서", "format": "int32", "example": 1}, "boxNum": {"type": "integer", "description": "박스번호", "format": "int32", "example": 123}, "realDistance": {"type": "number", "description": "실제 이동거리(m)", "format": "double", "example": 46349.7706334733}, "realTime": {"type": "integer", "description": "실제 소요시간(sec)", "format": "int64", "example": 2580}, "realStartTime": {"$ref": "#/components/schemas/LocalTime"}, "realArrivalTime": {"$ref": "#/components/schemas/LocalTime"}, "realEndTime": {"$ref": "#/components/schemas/LocalTime"}, "realServiceDuration": {"type": "integer", "description": "실제 서비스 소요시간(sec)", "format": "int64", "example": 10}, "routePath": {"type": "array", "description": "경로", "items": {"$ref": "#/components/schemas/CoordinateDTO"}}, "predictionDistance": {"type": "number", "description": "예상 이동거리(m)", "format": "double", "example": 50.0088315693237}, "predictionTime": {"type": "integer", "description": "예상 소요시간(sec)", "format": "int64", "example": 30}, "routePathReal": {"type": "array", "description": "실제 다중경로", "items": {"$ref": "#/components/schemas/CoordinateDTO"}}, "multiRoutePathReal": {"type": "array", "description": "실제 다중경로 목록", "items": {"type": "array", "description": "실제 다중경로 목록", "items": {"$ref": "#/components/schemas/CoordinateDTO"}}}, "estimatedMetersReal": {"type": "number", "description": "실제 이동거리(m)", "format": "double", "example": 24.3156932372912}, "estimatedSecondsReal": {"type": "integer", "description": "실제 소요시간(sec)", "format": "int64", "example": 30}, "etaDateTimeReal": {"type": "string", "description": "실제 예상도착일시", "format": "date-time"}, "fileCountForCompleted": {"type": "integer", "description": "배송이미지 갯수", "format": "int64", "example": 1}, "allocations": {"type": "array", "description": "배송할당 목록", "items": {"$ref": "#/components/schemas/DeliveryAllocationDTO"}}, "pickupPlace": {"$ref": "#/components/schemas/PickupPlaceDTO"}, "projectId": {"type": "integer", "description": "프로젝트 ID", "format": "int64", "example": 13342}, "callUserId": {"type": "integer", "description": "콜앱 사용자 ID", "format": "int64", "example": 8375}, "isNewDelivery": {"type": "boolean", "description": "신규배송 여부", "example": false}, "hailingType": {"type": "integer", "description": "헤일링 유형 (1:pickup, 2:drop)", "format": "int32", "example": 1}, "isSendingToCall": {"type": "boolean", "description": "콜앱에 전송 여부", "example": false}, "userDefinedOrderNum": {"type": "integer", "description": "사용자정의 배송순서", "format": "int32", "example": 1}, "inspectionStatus": {"type": "string", "description": "검수상태", "example": "PICKUP", "enum": ["미검수", "상차검수", "하차검수", "해당없음"]}, "pickupQuantity": {"type": "integer", "description": "상차수량", "format": "int64", "example": 1}, "dropoffQuantity": {"type": "integer", "description": "하차수량", "format": "int64", "example": 1}, "isInspectionEnded": {"type": "boolean", "description": "검수마감 여부", "example": false}, "qrBarCode": {"type": "string", "description": "QR/바코드", "example": "702430068959"}, "orderItemList": {"type": "array", "description": "상품 주문 목록", "items": {"$ref": "#/components/schemas/OrderItemDTO"}}, "isOnDemand": {"type": "boolean", "description": "실시간배송 여부", "example": false}, "isImmediate": {"type": "boolean", "description": "즉시배송 여부", "example": false}, "splitNumber": {"type": "integer", "description": "분할배송 번호", "format": "int32", "example": 1}, "senderName": {"type": "string", "description": "발신인", "example": "김발신"}, "senderPhoneNumber": {"type": "string", "description": "발신인 전화번호", "example": "01098765432"}, "senderCompanyName": {"type": "string", "description": "발신인 회사명", "example": "로지스텍"}, "senderImageUrl": {"type": "string", "description": "발신인 이미지 URL", "example": "https://file-examples.com/wp-content/uploads/2018/03/file_example_TIFF_1MB.tiff"}, "senderMemo": {"type": "string", "description": "발신인 메모", "example": "새해 복 많이 받으세요"}, "senderBaseAddress": {"type": "string", "description": "발신인 기본주소", "example": "서울 노원구 상계로 193-14"}, "senderDetailAddress": {"type": "string", "description": "발신인 상세주소", "example": "123동 1234호"}, "orderAmount": {"type": "integer", "description": "주문금액", "format": "int32", "example": 32000}, "uniqueCode": {"type": "string", "description": "주문정보 고유코드", "example": "DO47369"}, "warehouseCode": {"type": "string", "description": "창고코드", "example": "F01"}, "memo": {"type": "string", "description": "기사의 배송 메모", "example": "파손주의"}, "deliveryCategory": {"type": "string", "description": "배송 카테고리", "example": "근거리배송", "enum": ["근거리배송", "원거리배송", "명절배송"]}, "deliveryTime": {"type": "string", "description": "배송일자", "example": "20240521"}, "joCd": {"type": "string", "description": "조코드", "example": "001"}, "whGubn": {"type": "integer", "description": "본부/일반 구분", "format": "int32", "example": 1}, "storeCd": {"type": "string", "description": "점코드", "example": "무역센터점"}, "storageType": {"type": "string", "description": "호차구분", "example": "1-1 일반상온"}, "pickupVerificationStatus": {"type": "string", "description": "분류검증상태", "example": "VERIFIED", "enum": ["미검증", "검증", "해당없음"]}, "verificationQuantity": {"type": "integer", "description": "검증수량", "format": "int64", "example": 1}, "customerServiceContact": {"type": "string", "description": "고객 서비스 연락처", "example": "02-2662-2234"}, "productQr": {"type": "string", "description": "상품QR", "example": "M9999999999999"}, "businessCardQr": {"type": "string", "description": "명함QR", "example": "M999999999"}, "businessCard": {"type": "string", "description": "명함 유무", "example": "Y"}, "csCheckingName": {"type": "string", "description": "관리자 이름", "example": "logisteq"}, "estimatedDtOfArrival": {"type": "string", "description": "예상도착일시", "format": "date-time"}, "csChecking": {"type": "boolean"}}}, "DeliveryEndedDTO": {"required": ["deliveryStatus", "projectId", "riderId"], "type": "object", "properties": {"projectId": {"type": "integer", "description": "프로젝트 ID", "format": "int64", "example": 13342}, "riderId": {"type": "integer", "description": "기사 ID", "format": "int64", "example": 6433}, "deliveryId": {"type": "integer", "description": "배송 ID", "format": "int64", "example": 1411651}, "isForced": {"type": "boolean", "description": "강제여부", "example": true}, "deliveryStatus": {"type": "string", "description": "배송 상태", "example": "COMPLETED", "enum": ["배차대기", "배송전", "배송중", "서비스중", "거절", "배송실패", "배송완료", "미배송"]}, "deliveryCompletedType": {"type": "string", "description": "배송완료 유형", "example": "NORMAL_DELIVERY", "enum": ["NORMAL_DELIVERY", "CORRESPONDENCE", "COMMON_ENTRANCE", "SECURITY_OFFICE", "OTHER", "RETRIEVAL", "TRANSFER_DELIVERY", "NORMAL_DELIVERY_CONTACT", "DOOR", "STORAGE_BOX"]}, "deliveryCompletedMessage": {"type": "string", "description": "배송완료 메시지", "example": "3층문앞에 두었습니다 확인바랍니다"}, "deliveryFailureType": {"type": "string", "description": "배송실패 유형", "example": "WRONG_ADDR", "enum": ["NOBODY_HOME", "WRONG_ADDR", "LOST_ITEM", "BROKEN_ITEM", "OTHER", "MISDELIVERY", "TRANSFER_ITEM", "ORDER_CANCELLATION", "DISASTER", "CUST_REFUSE", "APPOINTED_DATE", "DELAYED_DELIVERY", "UNVISIT", "POSTPONEMENT", "DELIVERY_CANCELLATION", "QR_VERIFICATION_FAIL"]}, "deliveryFailureMessage": {"type": "string", "description": "배송실패 메시지", "example": "영업사원이 이미 수거해 감"}}}, "DeliveryMemoDTO": {"required": ["riderId"], "type": "object", "properties": {"riderId": {"type": "integer", "format": "int64"}, "memo": {"type": "string"}}}, "DeliveryRiderOrderDTO": {"type": "object", "properties": {"projectId": {"minimum": 1, "type": "integer", "description": "프로젝트 ID", "format": "int64", "example": 13342}, "riderId": {"minimum": 1, "type": "integer", "description": "기사 ID", "format": "int64", "example": 6433}, "toOrderNumber": {"minimum": 1, "type": "integer", "description": "변경할 순서 번호", "format": "int32", "example": 2}}}, "DeliveryRoutesRequestDTO": {"required": ["deliveryId", "order"], "type": "object", "properties": {"deliveryId": {"type": "integer", "description": "배송 아이디", "format": "int64"}, "order": {"type": "integer", "description": "배송 순서", "format": "int32"}, "distanceToArrival": {"type": "number", "description": "도착지까지의 거리 (미터)", "format": "double"}, "eta": {"type": "integer", "description": "도착지까지의 예상소요시간 (분)", "format": "int64"}, "timestamp": {"type": "string", "description": "클라이언트에서 올려준 시각", "format": "date-time"}, "routes": {"type": "array", "description": "이동 경로", "items": {"$ref": "#/components/schemas/RoutesCoordinate"}}}}, "DeliveryStatusDTO": {"required": ["deliveryStatus"], "type": "object", "properties": {"deliveryStatus": {"type": "string", "description": "배송 상태", "example": "COMPLETED", "enum": ["배차대기", "배송전", "배송중", "서비스중", "거절", "배송실패", "배송완료", "미배송"]}, "deliveryFailureType": {"type": "string", "description": "배송실패 유형", "example": "WRONG_ADDR", "enum": ["NOBODY_HOME", "WRONG_ADDR", "LOST_ITEM", "BROKEN_ITEM", "OTHER", "MISDELIVERY", "TRANSFER_ITEM", "ORDER_CANCELLATION", "DISASTER", "CUST_REFUSE", "APPOINTED_DATE", "DELAYED_DELIVERY", "UNVISIT", "POSTPONEMENT", "DELIVERY_CANCELLATION", "QR_VERIFICATION_FAIL"]}, "deliveryFailureMessage": {"type": "string", "description": "배송실패 메시지", "example": "영업사원이 이미 수거해 감"}, "deliveryCompletedType": {"type": "string", "description": "배송완료 유형", "example": "NORMAL_DELIVERY", "enum": ["NORMAL_DELIVERY", "CORRESPONDENCE", "COMMON_ENTRANCE", "SECURITY_OFFICE", "OTHER", "RETRIEVAL", "TRANSFER_DELIVERY", "NORMAL_DELIVERY_CONTACT", "DOOR", "STORAGE_BOX"]}, "deliveryCompletedMessage": {"type": "string", "description": "배송완료 메시지", "example": "3층문앞에 두었습니다 확인바랍니다"}}}, "DepartmentDTO": {"type": "object", "properties": {"departmentId": {"type": "integer", "description": "부서 ID", "format": "int64", "example": 37}, "organizationId": {"type": "integer", "description": "조직 ID", "format": "int64", "example": 1}, "parentDepartmentId": {"type": "integer", "description": "상위 부서 ID", "format": "int64", "example": 13}, "childDepartmentIdList": {"type": "array", "description": "하위 부서 ID 목록", "example": [14, 15], "items": {"type": "integer", "description": "하위 부서 ID 목록", "format": "int64"}}, "departmentName": {"type": "string", "description": "부서명", "example": "서대문홍은"}, "departmentCode": {"type": "string", "description": "부서 코드", "example": "59"}, "departmentLevel": {"type": "integer", "description": "레벨", "format": "int32", "example": 4}, "addressBase": {"type": "string", "description": "주소", "example": "서울 성동구 왕십리로 58"}, "addressDetail": {"type": "string", "description": "상세주소", "example": "포휴 503호"}, "active": {"type": "boolean", "description": "활성화 여부", "example": true}}}, "FileDTO": {"type": "object", "properties": {"fileId": {"type": "integer", "description": "파일 ID", "format": "int64", "example": 1446936}, "fileCategory": {"type": "string", "description": "파일 카테고리", "example": "DELIVERY_COMPLETE", "enum": ["RIDER_PROFILE", "DELIVERY_PRODUCT", "DELIVERY_COMPLETE", "WEB_USER_PROFILE"]}, "fileUrl": {"type": "string", "description": "파일 URL", "example": "https://lmd.logisteq.com/files/public/project/36248/delivery_complete/144294652/18db12f1cc.jpg"}}}, "GrantedAuthority": {"type": "object", "properties": {"authority": {"type": "string"}}}, "HubDTO": {"type": "object", "properties": {"hubId": {"type": "integer", "description": "허브 ID", "format": "int64", "example": 1}, "organizationId": {"type": "integer", "description": "조직 ID", "format": "int64", "example": 1}, "hubName": {"type": "string", "description": "허브명", "example": "양지Hub"}, "hubCode": {"type": "string", "description": "허브 코드", "example": "A001"}, "active": {"type": "boolean", "description": "활성화 여부", "example": true}}}, "LocalTime": {"type": "object", "properties": {"hour": {"type": "integer", "format": "int32"}, "minute": {"type": "integer", "format": "int32"}, "second": {"type": "integer", "format": "int32"}, "nano": {"type": "integer", "format": "int32"}}, "description": "실제 완료시각", "example": "22:14:58"}, "OrderItemDTO": {"type": "object", "properties": {"itemCode": {"type": "string", "description": "상품코드", "example": "C25480"}, "productBarcode": {"type": "string", "description": "바코드", "example": "87033367"}, "itemName": {"type": "string", "description": "상품명", "example": "삼다수 2L"}, "orderQuantity": {"type": "integer", "description": "주문수량", "format": "int64", "example": 1}, "storageType": {"type": "string", "description": "보관유형", "example": "상온"}, "boxNumber": {"type": "string", "description": "박스번호", "example": "123"}}, "description": "상품 주문 목록"}, "PageDeliveryDTO": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "pageable": {"$ref": "#/components/schemas/Pageable"}, "last": {"type": "boolean"}, "first": {"type": "boolean"}, "sort": {"$ref": "#/components/schemas/Sort"}, "numberOfElements": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/DeliveryDTO"}}, "number": {"type": "integer", "format": "int32"}, "empty": {"type": "boolean"}}}, "PageRequestDTO": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "direction": {"type": "string", "enum": ["ASC", "DESC"]}, "sortFieldName": {"type": "string"}}}, "Pageable": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "paged": {"type": "boolean"}, "unpaged": {"type": "boolean"}, "pageNumber": {"type": "integer", "format": "int32"}, "sort": {"$ref": "#/components/schemas/Sort"}, "offset": {"type": "integer", "format": "int64"}}}, "PickupPlaceDTO": {"type": "object", "properties": {"id": {"type": "integer", "description": "픽업장소 ID", "format": "int64", "example": 1}, "name": {"type": "string", "description": "픽업장소명", "example": "서울시청"}, "address": {"$ref": "#/components/schemas/AddressDTO"}}, "description": "픽업장소 정보"}, "ProjectAttribute": {"type": "object", "properties": {"isAnonymous": {"type": "boolean", "description": "익명 사용자에 의한 프로젝트 생성 여부", "example": false}, "isReadOnly": {"type": "boolean", "description": "기사, 방문지 편집 가능 여부", "example": false}, "isClusterDone": {"type": "boolean", "description": "클러스터링 완료 여부", "example": false}, "isFirstRoutingDone": {"type": "boolean", "description": "경로 생성(false), 재탐색(true) 구분", "example": false}, "isRouteEnabled": {"type": "boolean", "description": "경로 탐색 활성화 여부", "example": false}, "isSendingRiderEnabled": {"type": "boolean", "description": "기사에게 전송 활성화 여부", "example": false}, "isProductCheckEnabled": {"type": "boolean", "description": "상품 검수 활성화 여부", "example": false}, "isOnDemandEnabled": {"type": "boolean", "description": "실시간 프로젝트 여부", "example": false}, "createFrom": {"type": "string", "description": "프로젝트 생성 방법", "example": "DTO", "enum": ["EXCEL", "DTO", "DTO_READONLY", "COPY", "TRANSFER", "WEB_DTO_TEMP_PROJECT_ROUTE", "WEB_DTO_TEMP_PROJECT_CLUSTERING"]}, "clusterRule": {"type": "string", "description": "정적 배차 옵션", "example": "NORMAL", "enum": ["NORMAL", "EQUALITY", "LOCATION", "NUMBER"]}, "projectLoadingMode": {"type": "string", "description": "프로젝트 로드 모드", "example": "AUTO", "enum": ["AUTO", "GROUP", "GROUP_ORDER"]}, "routeOption": {"type": "integer", "description": "경로 탐색 옵션", "format": "int32", "example": 0}, "clusterRuleOnDemand": {"type": "string", "description": "실시간 동적 배차 옵션", "example": "ETA", "enum": ["ETA", "DISTANCE_TO_RIDER", "DELIVERY_COUNT"]}, "isAddDeliveryToLastOrder": {"type": "boolean", "description": "배송지 추가시 마지막 순서로 설정할지 여부", "example": false}, "isAutoCreated": {"type": "boolean", "description": "자동으로 생성된 프로젝트인지 여부", "example": false}, "isRouteExecute": {"type": "boolean", "description": "경로탐색 실행 여부", "example": false}, "isDriverDispatchEnabled": {"type": "boolean", "description": "작업지시서 활성화 여부", "example": false}}, "description": "프로젝트 속성"}, "RiderDTO": {"type": "object", "properties": {"id": {"type": "integer", "description": "기사 ID", "format": "int64", "example": 6433}, "name": {"type": "string", "description": "기사 성명", "example": "김배송"}, "mobile": {"type": "string", "description": "기사 전화번호", "example": "01012345678"}, "email": {"type": "string", "description": "기사 이메일", "example": "<EMAIL>"}, "password": {"type": "string", "description": "기사 비밀번호", "example": "plmkoiJn94**f"}, "linkStatus": {"type": "string", "description": "기사 연결상태", "example": "연결해제"}, "projectIds": {"type": "array", "description": "기사에게 할당된 프로젝트 ID 목록", "example": [132372, 132375], "items": {"type": "integer", "description": "기사에게 할당된 프로젝트 ID 목록", "format": "int64"}}, "departmentCodeList": {"type": "array", "description": "기사가 소속된 부서 코드 목록", "example": "['57']", "items": {"type": "string", "description": "기사가 소속된 부서 코드 목록", "example": "['57']"}}, "createAt": {"type": "string", "description": "기사 등록일시", "format": "date-time"}, "updateAt": {"type": "string", "description": "기사 변경일시", "format": "date-time"}, "vehicleType": {"type": "string", "description": "차량유형", "example": "트럭_탑", "enum": ["트럭_카고", "트럭_탑", "트럭_윙바디", "트럭_냉장", "트럭_냉동", "트럭_리프트", "트럭_무진동", "승합차", "승용차", "다마스", "이륜차", "도보", "암롤차량", "압축진개차량", "일반차량"]}, "overallLength": {"type": "integer", "description": "전장(mm)", "format": "int32", "example": 4750}, "overallWidth": {"type": "integer", "description": "전폭(mm)", "format": "int32", "example": 1820}, "overallHeight": {"type": "integer", "description": "전고(mm)", "format": "int32", "example": 1670}, "grossVehicleWeight": {"type": "number", "description": "차량총중량(kg)", "format": "float", "example": 2507.8}, "position": {"type": "string", "description": "직급", "example": "대리"}, "employeeNumber": {"type": "string", "description": "사번", "example": "240106001"}, "skillLevel": {"type": "integer", "description": "숙련도(%)", "format": "int32", "example": 80}, "isDeleted": {"type": "boolean", "description": "삭제여부", "example": false}, "workAuthority": {"type": "string", "description": "업무권한", "example": "DELIVERY", "enum": ["DELIVERY", "SCANNING", "DELIVERY_AND_SCANNING"]}, "autoCreate": {"type": "boolean", "description": "자동생성여부", "example": false}}}, "RiderMessageDTO": {"required": ["message", "projectId", "riderId"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "riderId": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}, "projectId": {"type": "integer", "format": "int64"}, "message": {"type": "string"}, "createAt": {"type": "string", "format": "date-time"}}}, "RiderProjectDTO": {"type": "object", "properties": {"id": {"type": "integer", "description": "프로젝트 ID", "format": "int64", "example": 13342}, "name": {"type": "string", "description": "프로젝트명", "example": "msft_20231201_서울강남"}, "codeName": {"type": "string", "description": "고객사 코드", "example": "msft"}, "effectiveDateTime": {"type": "string", "description": "프로젝트 실행일시", "format": "date-time"}, "doneDateTime": {"type": "string", "description": "프로젝트 종료일시", "format": "date-time"}, "completeDateTime": {"type": "string", "description": "프로젝트 완료일시", "format": "date-time"}, "attribute": {"$ref": "#/components/schemas/ProjectAttribute"}, "cutoffTime": {"type": "string", "description": "프로젝트 마감일시", "format": "date-time"}, "vehicleId": {"type": "integer", "description": "차량 ID", "format": "int64", "example": 196}, "vehicleType": {"type": "string", "enum": ["트럭_카고", "트럭_탑", "트럭_윙바디", "트럭_냉장", "트럭_냉동", "트럭_리프트", "트럭_무진동", "승합차", "승용차", "다마스", "이륜차", "도보", "암롤차량", "압축진개차량", "일반차량"]}, "overallLength": {"type": "integer", "description": "전장(mm)", "format": "int32", "example": 4750}, "overallWidth": {"type": "integer", "description": "전폭(mm)", "format": "int32", "example": 1820}, "overallHeight": {"type": "integer", "description": "전고(mm)", "format": "int32", "example": 1670}, "grossVehicleWeight": {"type": "number", "format": "float"}, "projectPushedAt": {"type": "string", "description": "기사에게 프로젝트를 전송한 일시", "format": "date-time"}, "createAt": {"type": "string", "description": "생성일시", "format": "date-time"}, "updateAt": {"type": "string", "description": "변경일시", "format": "date-time"}}}, "RoutesCoordinate": {"type": "object", "properties": {"x": {"type": "number", "format": "double"}, "y": {"type": "number", "format": "double"}, "distanceMeter": {"type": "integer", "format": "int32"}}, "description": "이동 경로"}, "Sort": {"type": "object", "properties": {"unsorted": {"type": "boolean"}, "sorted": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "VehicleDTO": {"type": "object", "properties": {"vehicleId": {"type": "integer", "description": "차량 ID", "format": "int64", "example": 196}, "licensePlate": {"type": "string", "description": "차량번호", "example": "50다3619"}, "vehicleModel": {"$ref": "#/components/schemas/VehicleModelDTO"}, "registerDt": {"type": "string", "description": "차량등록일시", "format": "date-time"}, "premiumDueDt": {"type": "string", "description": "자동차보험 만기일시", "format": "date-time"}, "safetyInspectionDt": {"type": "string", "description": "정기검사일시", "format": "date-time"}, "ownerId": {"type": "integer", "description": "소유자 ID", "format": "int64", "example": 123}, "scrapDt": {"type": "string", "description": "폐차예정일시", "format": "date-time"}, "drivetrainLoss": {"type": "number", "description": "구동계손실률", "format": "float", "example": 0.25}, "drivetrainEfficiency": {"type": "number", "description": "구동계효율", "format": "float", "example": 0.75}, "rollingResistance": {"type": "number", "description": "회전저항계수", "format": "float", "example": 0.012}, "dragCoefficient": {"type": "number", "description": "항력계수", "format": "float", "example": 0.28}, "engineEfficiency": {"type": "number", "description": "엔진효율", "format": "float", "example": 0.19}, "createAt": {"type": "string", "description": "생성일시", "format": "date-time"}, "updateAt": {"type": "string", "description": "변경일시", "format": "date-time"}, "vehicleModelId": {"type": "integer", "description": "차량모델 ID", "format": "int64", "writeOnly": true, "example": 345}, "isDispatching": {"type": "boolean", "description": "배차여부", "example": false}, "currentPassengerCapacity": {"type": "integer", "description": "현재탑승인원", "format": "int32", "example": 2}, "currentWeightCapacity": {"type": "integer", "description": "현재탑승중량(kg)", "format": "int32", "example": 130}, "currentDistanceCapacity": {"type": "integer", "description": "현재주행가능거리(km)", "format": "int32", "example": 100}, "maxDistanceCapacity": {"type": "integer", "description": "최대주행가능거리(km)", "format": "int32", "example": 300}, "hasRiderId": {"type": "boolean", "description": "기사 ID 연결 여부", "writeOnly": true, "example": false}, "vehicleType": {"type": "string", "description": "차량유형", "writeOnly": true, "example": "트럭_카고", "enum": ["트럭_카고", "트럭_탑", "트럭_윙바디", "트럭_냉장", "트럭_냉동", "트럭_리프트", "트럭_무진동", "승합차", "승용차", "다마스", "이륜차", "도보", "암롤차량", "압축진개차량", "일반차량"]}, "fuelType": {"type": "string", "description": "연료유형", "writeOnly": true, "example": "전기", "enum": ["휘발유", "경유", "LPG", "LNG", "CNG", "수소전지", "전기"]}, "passengerCapacity": {"type": "integer", "description": "탑승가능인원", "format": "int32", "writeOnly": true, "example": 5}, "weightCapacity": {"type": "integer", "description": "탑승가능중량", "format": "int32", "writeOnly": true, "example": 300}, "distanceCapacity": {"type": "integer", "description": "주행가능거리(km)", "format": "int32", "writeOnly": true, "example": 250}}}, "VehicleModelDTO": {"type": "object", "properties": {"vehicleModelId": {"type": "integer", "description": "차량모델 ID", "format": "int64", "example": 16}, "modelName": {"type": "string", "description": "모델명", "example": "포터 2"}, "vehicleType": {"type": "string", "description": "차량유형", "example": "트럭_카고", "enum": ["트럭_카고", "트럭_탑", "트럭_윙바디", "트럭_냉장", "트럭_냉동", "트럭_리프트", "트럭_무진동", "승합차", "승용차", "다마스", "이륜차", "도보", "암롤차량", "압축진개차량", "일반차량"]}, "sizeType": {"type": "string", "description": "차량크기유형", "example": "중형", "enum": ["소형", "중형", "대형"]}, "fuelType": {"type": "string", "description": "연료유형", "example": "전기", "enum": ["휘발유", "경유", "LPG", "LNG", "CNG", "수소전지", "전기"]}, "wheelDrvType": {"type": "string", "description": "휠구동방식", "example": "전륜구동 (2륜)", "enum": ["전륜구동 (2륜)", "후륜구동 (2륜)", "풀타임 사륜구동", "사륜구동 (2륜 or 4륜)"]}, "cargoType": {"type": "string", "description": "화물유형", "example": "화물", "enum": ["승용", "화물", "기타"]}, "fuelEfficiency": {"type": "number", "description": "연비", "format": "float", "example": 15.3}, "numberOfPassengers": {"type": "integer", "description": "승차인원", "format": "int32", "example": 2}, "vehicleMass": {"type": "number", "description": "공차중량(kg)", "format": "float", "example": 1250.5}, "maximumPayload": {"type": "number", "description": "최대적재중량(kg)", "format": "float", "example": 1050.5}, "grossVehicleWeight": {"type": "number", "description": "차량총중량(kg)", "format": "float", "example": 2507.8}, "overallLength": {"type": "integer", "description": "전장(mm)", "format": "int32", "example": 4750}, "overallWidth": {"type": "integer", "description": "전폭(mm)", "format": "int32", "example": 1820}, "overallHeight": {"type": "integer", "description": "전고(mm)", "format": "int32", "example": 1670}, "frontalAreaSQFT": {"type": "number", "description": "전면부면적(ft2)", "format": "float", "example": 21.17}, "frontalAreaSQM": {"type": "number", "description": "전면부면적(m2)", "format": "float", "example": 1.96676}, "displacement": {"type": "integer", "description": "배기량(cc)", "format": "int32", "example": 1598}, "fuelTankCapacity": {"type": "number", "description": "연료탱크용량(L)", "format": "float", "example": 58.8}, "releaseDt": {"type": "string", "description": "출고일시", "format": "date-time"}, "createAt": {"type": "string", "description": "생성일시", "format": "date-time"}, "updateAt": {"type": "string", "description": "변경일시", "format": "date-time"}, "loadingLength": {"type": "integer", "description": "적재공간길이(m)", "format": "int32", "example": 100}, "loadingWidth": {"type": "integer", "description": "적재공간너비(m)", "format": "int32", "example": 20}, "loadingHeight": {"type": "integer", "description": "적재공간높이(m)", "format": "int32", "example": 50}}, "description": "차량모델 정보"}, "WebUserDetails": {"type": "object", "properties": {"password": {"type": "string"}, "username": {"type": "string"}, "authorities": {"type": "array", "items": {"$ref": "#/components/schemas/GrantedAuthority"}}, "accountNonExpired": {"type": "boolean"}, "accountNonLocked": {"type": "boolean"}, "credentialsNonExpired": {"type": "boolean"}, "enabled": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "organizationId": {"type": "integer", "format": "int64"}, "organizationName": {"type": "string"}, "organizationCode": {"type": "string"}, "departmentIdList": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "authcode": {"type": "string"}, "readonly": {"type": "boolean"}}}}, "securitySchemes": {"JWT Authentication": {"type": "http", "description": "A JWT token is required to access this API. JWT token can be obtained by providing correct username and password in the User API.", "name": "Authorization", "in": "header", "scheme": "Bearer", "bearerFormat": "JWT"}}}}