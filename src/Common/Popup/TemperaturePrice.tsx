import { useTranslation } from 'react-i18next';
import Layout from './Layout';
import CustomButton from '@/Common/Components/CustomButton.tsx';
import { AlertPopupProps } from '@/types';
import Input from '@/Common/Components/Input.tsx';
import close_popup from '@/assets/images/close_popup.png';
import Dropdown from '@/Common/Components/DropDown.tsx';

const TemperaturePrice = ({
  isOpen,
  title,
  onClose,
  onConfirm,
}: AlertPopupProps) => {
  const { t, i18n } = useTranslation();
  const isEnglish = i18n.language === 'en';

  const options = [
    { key: t('°F'), value: 'f' },
    { key: t('°C'), value: 'c' },
  ];

  return (
    <Layout isOpen={isOpen}>
      <div
        className={`p-10 flex-col bg-white space-y-10 ${isEnglish ? 'w-[480px]' : 'w-[640px]'}`}
      >
        <div className="w-full flex items-center justify-between">
          {title && (
            <div className="heading3">{t('SetTemperatureCriteria')}</div>
          )}
          <img
            src={close_popup}
            className="w-6 h-6 cursor-pointer"
            onClick={onClose}
          />
        </div>
        <div
          className={`w-full flex gap-6 ${isEnglish ? 'flex-col justify-start items-start gap-4 [&_span]:w-[165px]' : 'items-center [&_span]:w-[60px]'}`}
        >
          <div className={'flex items-center gap-3'}>
            <span className=" body1-b">{t('HighTemperature')}</span>
            <Input
              onChange={() => undefined}
              placeholder={t('HighTemperatureSet')}
              variant={'underbar'}
            />
          </div>
          <div className={'flex items-center gap-3'}>
            <span className=" body1-b">{t('LowTemperature')}</span>
            <Input
              onChange={() => undefined}
              placeholder={t('LowTemperatureSet')}
              variant={'underbar'}
            />
          </div>
          <div className={'flex items-center gap-3'}>
            <span className="body1-b">{t('TemperatureUnit')}</span>
            <Dropdown
              onChange={() => undefined}
              options={options}
              placeholder={'°F'}
              className="w-[102px] h-11"
            />
          </div>
        </div>
        <div className="w-full flex justify-end gap-3">
          <CustomButton size={'lg'} variant={'secondary'} onClick={onClose}>
            {t('Cancel')}
          </CustomButton>
          <CustomButton size={'lg'} variant={'first'} onClick={onConfirm}>
            {t('ConfirmS')}
          </CustomButton>
        </div>
      </div>
    </Layout>
  );
};

export default TemperaturePrice;
