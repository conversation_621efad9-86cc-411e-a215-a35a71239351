import { useTranslation } from 'react-i18next';
import Layout from './Layout';
import CustomButton from '@/Common/Components/CustomButton.tsx';
import { AlertPopupProps } from '@/types';
import Dropdown from '@/Common/Components/DropDown.tsx';
import Input from '@/Common/Components/Input.tsx';
import close_popup from '@/assets/images/close_popup.png';

const FuelPrice = ({ isOpen, title, onClose, onConfirm }: AlertPopupProps) => {
  const { t, i18n } = useTranslation();
  const isEnglish = i18n.language === 'en';

  const options1 = [
    { key: t('Gallon'), value: 'Gallon' },
    { key: t('L'), value: 'L' },
  ];

  const options2 = [
    { key: t('$'), value: '$' },
    { key: t('₩'), value: '₩' },
  ];

  return (
    <Layout isOpen={isOpen}>
      <div className="w-[480px] p-10 bg-white rounded">
        <div className="flex flex-col">
          <div className="mb-[34px] flex justify-between items-center">
            {title && (
              <div className="text-[22px] font-bold leading-[30.80px]">
                {title}
              </div>
            )}
            <img
              src={close_popup}
              className="w-6 h-6 cursor-pointer"
              onClick={onClose}
            />
          </div>
          <div className="flex flex-col gap-4">
            {/* 언어에 따라 flex-col 추가 */}
            <div
              className={`flex items-center ${isEnglish ? 'flex-col gap-4 [&_>div]:[&_>div]:w-[128px]' : ''}`}
            >
              <div className="w-full flex items-center gap-3">
                <div className="body1-b col-span-1">{t('FuelUnit')}</div>
                <Dropdown
                  className="w-[102px] h-11 col-span-1"
                  onChange={() => undefined}
                  options={options1}
                  placeholder={'Gallon'}
                />
              </div>
              <div className="w-full flex items-center gap-3">
                <div className="body1-b">{t('CurrencyUnit')}</div>
                <Dropdown
                  className="w-[102px] h-11"
                  onChange={() => undefined}
                  options={options2}
                  placeholder={'$'}
                />
              </div>
            </div>
            <div className="flex justify-center items-center gap-3 w-full">
              <div className="body1-b">{t('Cost')}</div>
              <Input
                width={'flex-1'}
                onChange={() => undefined}
                placeholder={t('Cost')}
                variant={'underbar'}
              />
            </div>
          </div>
        </div>
        <div className="mt-10 flex justify-end gap-3">
          <CustomButton size="lg" variant={'secondary'} onClick={onClose}>
            {t('Close')}
          </CustomButton>
          <CustomButton size="lg" variant={'first'} onClick={onConfirm}>
            {t('ConfirmS')}
          </CustomButton>
        </div>
      </div>
    </Layout>
  );
};

export default FuelPrice;
