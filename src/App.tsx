import 'remixicon/fonts/remixicon.css';
import Routing from './Route/Index';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
// import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import '@radix-ui/themes/styles.css';
import { Theme } from '@radix-ui/themes';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import 'react-dropdown/style.css';
import './assets/scss/react-datepicker.css';
import './assets/scss/Calendar.css';
import '@/i18n/i18n.js';
import { APIProvider } from '@vis.gl/react-google-maps';
// Fake backend
import { OverlayProvider } from '@toss/use-overlay';
import { UserLocationProvider } from './Common/Components/UserLocationContext';

const isDevelopment = process.env.NODE_ENV === 'development';
console.log(`현재 실행 모드: ${isDevelopment ? '개발 모드' : '프로덕션 모드'}`);

// Create a client
const queryClient = new QueryClient();

function App() {
  return (
    <div className="App">
      <QueryClientProvider client={queryClient}>
        <APIProvider
          apiKey={'AIzaSyAVcnsJVJNTOqkzry8m1eQiv6Y6xQVJYn4'}
          onLoad={() => console.log('Maps API has loaded.')}
        >
          <UserLocationProvider>
            <OverlayProvider>
              <Theme>
                <Routing />
              </Theme>
            </OverlayProvider>
          </UserLocationProvider>
        </APIProvider>
        {/* {isDevelopment && <ReactQueryDevtools initialIsOpen={false} />} */}
      </QueryClientProvider>
    </div>
  );
}

export default App;
