import { useTranslation } from 'react-i18next';
import React, { useState, useCallback } from 'react';
import { Link, useLocation } from 'react-router-dom';
import mainLogoB from '@/assets/images/logo/MainLogo.svg';
import down from '../assets/images/down.png';
import dropdown from '../assets/images/dropdown.png';
import expand from '../assets/images/Comp/GNB/ic/24/expand-rignt.svg';
import reduction from '../assets/images/Comp/GNB/ic/24/reduction.svg';
// import expendFixed from '../assets/images/Comp/GNB/ic/24/expand-fixed.svg';
import { hiMateSideBar, SidebarItem } from './SidebarData';
import { cn } from '@/Common/function/utils.ts';
import { useToast } from '@/Common/useToast.tsx';

// 비활성화된 메뉴를 위한 커스텀 토스트 컴포넌트
const UnsupportedMenuToast = () => {
  const { t } = useTranslation();

  const { toast } = useToast();

  // 비활성화된 메뉴 클릭 시 토스트 메시지 표시
  const showUnsupportedMessage = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      toast({
        types: 'warning',
        description: t('ItsNotYetAvailable'),
      });
    },
    [toast],
  );

  return { showUnsupportedMessage };
};

interface SidebarProps {
  isSidebarSize: boolean;
  onCollapse: (isOnCollapse: boolean) => void;
}

const Sidebar = ({ isSidebarSize, onCollapse }: SidebarProps) => {
  const { t } = useTranslation();
  const { showUnsupportedMessage } = UnsupportedMenuToast();

  const [isOnCollapse, setIsOnCollapse] = useState(false);

  const location = useLocation();
  const storedData: string | null = localStorage.getItem('activeMenu');
  const [activeMenu, setActiveMenu] = useState<number | null>(
    Number(storedData),
  );
  const toggleSubMenu = (menu: number) => {
    localStorage.setItem(
      'activeMenu',
      JSON.stringify(activeMenu === menu ? null : menu),
    );
    setActiveMenu(activeMenu === menu ? null : menu);
  };

  const sidebarClass = cn(
    isSidebarSize ? 'sidebar' : 'sidebar-small',
    'fixed z-50 h-full !rounded-b-none bg-[#010542]',
    isSidebarSize ? '!w-[240px]' : '',
  );

  const logoClass = cn('pt-10 pb-[22px] px-6', isSidebarSize ? '' : 'bg-black');
  return (
    <React.Fragment>
      <nav className={sidebarClass}>
        <div className="h-full dark:bg-darklight group-data-[sidebar=dark]/item:bg-darklight group-data-[sidebar=brand]/item:bg-sky-950 ">
          <div className={logoClass}>
            {isSidebarSize ? (
              <div className={'flex justify-between items-center'}>
                <div className={'mr-2'}>
                  <Link to="/" className="w-full main-logo">
                    <img
                      src={mainLogoB}
                      className=" dark-logo logo dark:hidden group-data-[sidebar=dark]/item:hidden group-data-[sidebar=brand]/item:hidden"
                      alt="logo"
                    />
                  </Link>
                </div>
                <button
                  onClick={() => {
                    const newValue = !isOnCollapse;
                    setIsOnCollapse(newValue);
                    onCollapse(newValue);
                  }}
                >
                  <img src={reduction} alt={'reduction'} />
                </button>
              </div>
            ) : (
              <button className="w-6 h-6">
                <img src={expand} alt={'expand'} />
              </button>
            )}
          </div>
          <div className="no_scroll h-[calc(100vh-60px)] overflow-y-auto overflow-x-hidden pb-12 detached-menu flex flex-col justify-between">
            <ul className="relative flex flex-col gap-1 ">
              {(hiMateSideBar || []).map((item: SidebarItem, key: number) => (
                <React.Fragment key={key}>
                  {item.isDivide ? (
                    <hr className={'bg-[#B3B3B3] opacity-20 my-1'} />
                  ) : (
                    <li className={`menu nav-item w-[214px] hover:w-[214px]`}>
                      <Link
                        to={
                          item.disabled ? '#' : item.subItems ? '#' : item.link
                        }
                        className={`items-center justify-between text-white nav-link group flex ${
                          location.pathname === item.link ||
                          item.subItems?.some(
                            (subitem: SidebarItem) =>
                              location.pathname === subitem.link,
                          )
                            ? 'active'
                            : ''
                        }`}
                        onClick={(e) => {
                          if (item.disabled) {
                            showUnsupportedMessage(e);
                            return;
                          }
                          toggleSubMenu(key);
                        }}
                      >
                        <div
                          className={`flex items-center gap-[10px] w-[214px] hover:w-[214px] rounded-r-2xl 
                          ${!item.disabled ? 'hover:bg-[#FFFFFF] group' : ''} 
                          ${location.pathname === item.link && !item.disabled ? 'bg-[#FFFFFF]' : ''}
                          py-[14px] pl-6 pr-4`}
                        >
                          <img
                            src={
                              location.pathname === item.link ||
                              item.disabled === false
                                ? item.iconHover
                                : item.icon
                            }
                            alt="icon"
                            className={`w-6 h-6 
                              ${item.disabled ? 'opacity-50' : ''} 
                              ${!item.disabled ? 'group-hover:brightness-0' : ''}
                            `}
                          />
                          {isSidebarSize && (
                            <>
                              <span
                                className={`body2-b text-wrap whitespace-pre-line w-full
                              ${
                                item.disabled
                                  ? 'text-gray-400'
                                  : location.pathname === item.link
                                    ? 'text-[#010542]' // 활성화된 메뉴일 때 검정색
                                    : 'text-text-wh group-hover:text-[#010542]'
                              }`}
                                style={{
                                  letterSpacing: '-0.32px',
                                }}
                              >
                                {t(item.label)}
                              </span>
                              {item.subItems && !item.disabled && (
                                <div
                                  className={`flex items-center justify-center dropdown-icon w-6 ${
                                    activeMenu === key ? '!rotate-180' : ''
                                  }`}
                                >
                                  <img
                                    src={down}
                                    alt="icon"
                                    className={'w-6 h-6 group-hover:hidden'}
                                  />
                                  <img
                                    src={dropdown}
                                    alt="icon"
                                    className={
                                      'w-6 h-6 hidden group-hover:block'
                                    }
                                  />
                                </div>
                              )}
                            </>
                          )}
                        </div>
                        <div
                          className={`w-2 bg-white rounded-tl-3xl rounded-bl-3xl ${location.pathname === item.link && !item.disabled ? 'visible' : 'invisible'} `}
                        />
                      </Link>
                    </li>
                  )}
                  {item.subItems && !item.disabled && (
                    <ul
                      style={{
                        display:
                          isSidebarSize && activeMenu === key ? '' : 'none',
                      }}
                      className="sub-menu flex flex-col gap-1 text-white dark:text-white/60 group-data-[sidebar=dark]/item:text-white/60 group-data-[sidebar=brand]/item:text-sky-200"
                    >
                      {item.subItems?.map(
                        (subitem: SidebarItem, subKey: number) => (
                          <React.Fragment key={subKey}>
                            {!subitem.subItems && (
                              <li className={`menu nav-item w-[214px] hover:w-[214px]`}>
                                {
                                  <Link
                                    to={subitem.disabled ? '#' : subitem.link}
                                    className={` items-center justify-between text-white nav-link group ${
                                      location.pathname === subitem.link &&
                                      !subitem.disabled
                                        ? 'active'
                                        : ''
                                    }`}
                                    onClick={(e) => {
                                      if (subitem.disabled) {
                                        showUnsupportedMessage(e);
                                        return;
                                      }
                                    }}
                                  >
                                    <div
                                      className={cn(
                                        'flex items-center gap-[10px] w-[214px] h-auto hover:w-[214px] rounded-r-2xl group',
                                        location.pathname === subitem.link &&
                                          !subitem.disabled
                                          ? 'bg-[#FFFFFF]'
                                          : '',
                                        !subitem.disabled
                                          ? 'hover:bg-[#FFFFFF]'
                                          : '',
                                      )}
                                    >
                                      {/* {subitem.icon ? (
                                        <img
                                          src={subitem.icon}
                                          alt="icon"
                                          className={`w-6 h-6 ${subitem.disabled ? 'opacity-50' : ''}`}
                                        />
                                      ) : (
                                        <div className={'w-6 h-6'}></div>
                                      )} */}
                                      <span
                                        className={`body6-s py-[10px] pl-[58px] pr-4 w-auto text-wrap whitespace-pre-line
                                        ${
                                          subitem.disabled
                                            ? 'text-gray-400'
                                            : location.pathname === subitem.link
                                              ? 'text-[#010542]' // 활성화된 서브메뉴일 때 검정색
                                              : 'text-text-wh group-hover:text-[#010542]'
                                        }`}
                                        style={{
                                          letterSpacing: '-0.32px',
                                        }}
                                      >
                                        {subitem.label.includes('\n')
                                          ? subitem.label
                                              .split('\n')
                                              .map((s, index) => (
                                                <React.Fragment key={index}>
                                                  {t(s)} <br />
                                                </React.Fragment>
                                              ))
                                          : t(subitem.label)}
                                      </span>
                                    </div>
                                    <div
                                      className={`w-2 bg-white rounded-tl-3xl rounded-bl-3xl ${location.pathname === subitem.link && !subitem.disabled ? 'visible' : 'invisible'} `}
                                    />
                                  </Link>
                                }
                              </li>
                            )}
                          </React.Fragment>
                        ),
                      )}
                    </ul>
                  )}
                </React.Fragment>
              ))}
            </ul>
          </div>
        </div>
      </nav>
    </React.Fragment>
  );
};

export default Sidebar;
