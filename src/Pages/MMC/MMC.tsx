import { useEffect, useRef, useState } from 'react';
import ZoomController from '@/Common/Components/ZoomController.tsx';
import MMCSearchFilter from '@/Pages/MMC/components/MMCSearchFilter.tsx';
import { useLayoutStore } from '@/store/layout.ts';
import {
  EqSingleMarkerSet,
  EqGroupMarkerSet,
  MapHelper,
  MapUtils,
  MapRandomData,
} from '@/Common/constants/Maps.ts';
import FilteredMapListPopupButton from '@/Common/Components/FilteredMapListPopupButton.tsx';
import MMCFilteredMapList from '@/Pages/MMC/components/MMCFilteredMapList.tsx';
import EqSingleMarker, {
  EqSingleMarkerProps,
} from '@/Common/Components/Marker/EqSingleMarker';
import EqMmcInfoWindow, {
  MMCEpInfoWindowProps,
} from '@/Common/Components/InfoWindow/EqMmcInfoWindow';
import EqGroupMarker, {
  EqGroupMarkerProps,
} from '@/Common/Components/Marker/EqGroupMarker';
import useMMCPopup from '@/Pages/MMC/components/useMMCPopup.tsx';
import ApplyHistoryPopupButton from '@/Pages/MMC/components/ApplyHistoryPopupButton.tsx';
import StatisticsPopupButton from '@/Pages/MMC/components/StatisticsPopupButton.tsx';
import { useQuery } from '@tanstack/react-query';
import { mmcApi } from '@/api';
import GMap from '@/Common/Components/GMap';
import { DemoTest, MarkerMapItem, MMCEqItem, MMCSearchParams } from '@/types';

/**
 * UI.9.1.1.1 MMC / 지도 스크린
 */
const Mmc = () => {
  const { move: layoutMove } = useLayoutStore((state) => state);

  const [gMap, setGMap] = useState<google.maps.Map | null>(null);
  const mapSizeRef = useRef<{ width: number; height: number }>({
    width: 0,
    height: 0,
  });
  const latlngBoundsRef = useRef<google.maps.LatLngBoundsLiteral>({
    east: 180.0,
    north: 90.0,
    south: -90.0,
    west: -180.0,
  });
  const maxZoomLevel = 21;
  const minZoomLevel = 3;
  const defaultLevel = 15;
  const zoomLevelRef = useRef(defaultLevel);
  const [zoomGauge, setZoomGauge] = useState(defaultLevel);
  const markerTypeRef = useRef(0);
  //DataList
  const markerItemsRef = useRef<MarkerMapItem[]>([]);
  //Singles
  const [eqSingleMarkerSets] = useState<Map<number, EqSingleMarkerSet>>(
    new Map(),
  );
  const [eqSingleMarkerProps, setEqSingleMarkerProps] = useState<
    EqSingleMarkerProps[]
  >([]);
  const [eqSingleInfoWindowProps, setEqSingleInfoWindowProps] =
    useState<MMCEpInfoWindowProps>();
  //Groups
  const [eqGroupMarkerSets] = useState<Map<number, EqGroupMarkerSet>>(
    new Map(),
  );
  const [eqGroupMarkerProps, setEqGroupMarkerProps] = useState<
    EqGroupMarkerProps[]
  >([]);

  /** Query */

  const [mmcSearchDataParams, setMmcSearchDataParams] = useState({
    region: '',
    country: '',
    level: '',
    condition: '',
  });

  const [machineInfoParams, setMachineInfoParams] = useState({
    machineNo: '',
    latitude: 0.0,
    longitude: 0.0,
  });

  const { data: mmcSearchData } = useQuery({
    queryKey: ['mmc:trouble&consumables-list', mmcSearchDataParams],
    queryFn: async () => {
      try {
        if (DemoTest.isModeOn() == true) {
          const demoData = MapRandomData.queryMmcMarkers(32);

          if (demoData) {
            return demoData;
          }
        } else {
          const response = [];

          if (mmcSearchDataParams.level.length > 0) {
            const response0 = await mmcApi.getTroubleMMCMap({
              pRegion: mmcSearchDataParams.region,
              pCountry: mmcSearchDataParams.country,
              pLevel: mmcSearchDataParams.level,
            });
            response.push(response0.data);
          }

          if (mmcSearchDataParams.condition.length > 0) {
            const response1 = await mmcApi.getConsumablesMMCMap({
              pRegion: mmcSearchDataParams.region,
              pCountry: mmcSearchDataParams.country,
              pCondition: mmcSearchDataParams.condition,
            });
            response.push(response1.data);
          }

          const result: {
            machineNo: string;
            latitude: number;
            longitude: number;
          }[] = [];

          if (response.length >= 1) {
            for (const item of response[0]) {
              let flag = true;
              if (response.length >= 2) {
                flag = response[1].some(
                  (_item) => _item.machineNo === item.machineNo,
                );
              }
              if (flag && item.machineNo && item.latitude && item.longitude) {
                result.push({
                  latitude: item.latitude,
                  longitude: item.longitude,
                  machineNo: item.machineNo,
                });
              }
            }
          }

          if (result.length > 0) {
            return result;
          }
        }
        return [];
      } catch (error) {
        console.error('API 호출 에러:', error);
        throw error;
      }
    },
    enabled: mmcSearchDataParams.country.length > 0,
  });

  const { data: machineInfoData } = useQuery({
    queryKey: ['mmc:machineInfo', machineInfoParams],
    queryFn: async () => {
      try {
        if (DemoTest.isModeOn() == true) {
          const _mmcData = MapRandomData.queryMmcInfoData(
            machineInfoParams.machineNo,
            machineInfoParams.latitude,
            machineInfoParams.longitude,
          );

          if (_mmcData) {
            return [_mmcData];
          }
        } else {
          const response = await mmcApi.getMachineInfoData({
            machineNo: machineInfoParams.machineNo,
          });

          if (response.data) {
            return [response.data];
          }
        }
        return [];
      } catch (error) {
        console.error('API 호출 에러:', error);
        throw error;
      }
    },
    enabled: machineInfoParams.machineNo.length > 0,
  });

  const {
    openFocusEqPopup,
    // openRegionDeletePopup,
    // openEqDeletePopup,
    // openWorkerDeletePopup,
    // openRegionAddPopup,
    // openEqAddPopup,
    openApplyPopup,
  } = useMMCPopup();

  /** useEffect */

  //useQuery 결과 갱신되면 맵 업데이트
  useEffect(() => {
    if (mmcSearchData == null) {
      return;
    }

    if (gMap) {
      updateEqData();
    }
  }, [gMap, mmcSearchData]);

  useEffect(() => {
    if (gMap && machineInfoData) {
      if (machineInfoData.length > 0) {
        const resData = machineInfoData[0];
        if (resData.machineNo) {
          const _mmcData: MMCEqItem = {
            id: resData.machineNo,
            fleet: '', //플릿
            latlng: {
              lat: machineInfoParams.latitude,
              lng: machineInfoParams.longitude,
            },
            commType: '', //통신 방식
            machineType: '', //장비 타입
            modelName: '', //모델 명
            hogiNo: resData.machineNo, //호기
            machineNo: '', //장비 번호
            custNo: '', //관리 번호
            location: '', //위치
            dealer: '', //대리점
            hourmeter: '', //작동 시간
            service: {
              //서비스 상태
              startDate: '09-Apr-2013',
              endDate: '09-Apr-2018',
              status: 'InOperation',
            },
            eqStat: {
              //장비 상태
              idle: false,
              running: false,
              consumable: false,
              broken: false,
              nogps: false,
            },
            lastUpdate: '', //최종 업데이트 일시
          };

          setEqSingleInfoWindowProps((prevState) => {
            if (!prevState) {
              return prevState;
            } else {
              return {
                eqData: _mmcData,
                anchor: prevState.anchor,
                onClose: prevState.onClose,
                openFocusEqPopup: prevState.openFocusEqPopup,
                openApplyPopup: prevState.openApplyPopup,
              };
            }
          });
        }
      }
    }
  }, [gMap, machineInfoData]);

  /** Event Listener */

  //지도 로드되면 기본 검색
  const handleMapInit = (map: google.maps.Map) => {
    setGMap(map);

    if (DemoTest.isModeOn() == true) {
      handleSearch({
        country: 'KR',
        faultPart: '',
        faultSeverity: {
          warning: false,
          service_soon: false,
          service_now: false,
          stop_soon: false,
          stop_now: false,
        },
        consumableStatus: {
          deadlineDue: false,
          deadlineExceeded: false,
        },
      });
    }
  };

  //지도 클릭되면 InfoWindow 닫기
  const handleMapClick = () => {
    setEqSingleInfoWindowProps(undefined);
  };

  //지도 스케일 변경되면 마커 업데이트
  const handleMapZoomChanged = (zoom: number) => {
    zoomLevelRef.current = zoom;
    setZoomGauge(zoom);
    updateEqMarkers();
  };

  //지도 화면 사이즈 체크
  const handleMapSizeChanged = (width: number, height: number) => {
    mapSizeRef.current.width = width;
    mapSizeRef.current.height = height;
  };

  //지도 사이즈 체크
  const handleMapBoundsChanged = (bounds: google.maps.LatLngBoundsLiteral) => {
    latlngBoundsRef.current = bounds;
    updateEqMarkers();
  };

  //핀 마커 클릭하면 InfoWindow 출력
  const handleClickSingleMark = (
    id: string,
    anchor?: google.maps.marker.AdvancedMarkerElement,
  ) => {
    if (!anchor) {
      setEqSingleInfoWindowProps(undefined);
    } else {
      const result = markerItemsRef.current.find((data) => data.id == id);
      if (!result) {
        setEqSingleInfoWindowProps(undefined);
      } else {
        setEqSingleInfoWindowProps({
          item: undefined,
          anchor: anchor,
          onClose: handleCloseSingleInfoWindow,
          openFocusEqPopup: openFocusEqPopup,
          openApplyPopup: openApplyPopup,
        });

        setMachineInfoParams({
          machineNo: id,
          latitude: result.latlng.lat,
          longitude: result.latlng.lng,
        });
      }
    }
  };

  //InfoWindow 닫기 이벤트
  const handleCloseSingleInfoWindow = () => {
    setEqSingleInfoWindowProps(undefined);
  };

  // const onClickGroupMark = (id: string, anchor?: google.maps.marker.AdvancedMarkerElement) => {
  // };

  //검색 버튼 클릭
  const handleSearch = (params: MMCSearchParams) => {
    let levelStr = '';

    if (params.faultSeverity.warning) {
      levelStr += '1,';
    }
    if (params.faultSeverity.service_soon) {
      levelStr += '2,';
    }
    if (params.faultSeverity.service_now) {
      levelStr += '3,';
    }
    if (params.faultSeverity.stop_soon) {
      levelStr += '4,';
    }
    if (params.faultSeverity.stop_now) {
      levelStr += '5,';
    }

    if (levelStr.endsWith(',')) {
      levelStr = levelStr.slice(0, -1);
    }

    let conditionStr = '';
    if (
      params.consumableStatus.deadlineDue &&
      params.consumableStatus.deadlineExceeded
    ) {
      conditionStr = 'ALL';
    } else {
      if (params.consumableStatus.deadlineDue) {
        conditionStr = 'S';
      }
      if (params.consumableStatus.deadlineExceeded) {
        conditionStr = 'O';
      }
    }

    setMmcSearchDataParams({
      region: params.country,
      country: params.country,
      level: levelStr,
      condition: conditionStr,
    });
  };

  /** Function */

  //장비 정보(markerItemsRef) 업데이트
  const updateEqData = () => {
    const bounds = new google.maps.LatLngBounds();

    markerItemsRef.current = [];
    mmcSearchData?.forEach((data, idx) => {
      const latlng = new google.maps.LatLng(data.latitude, data.longitude);
      bounds.extend(latlng);

      markerItemsRef.current.push({
        id: data.machineNo ?? idx.toString(),
        latlng: {
          lat: latlng.lat(),
          lng: latlng.lng(),
        },
        eqStat: undefined,
      });
    });

    if (markerItemsRef.current.length > 0) {
      fitBounds(bounds);
    }

    eqSingleMarkerSets.clear();
    eqGroupMarkerSets.clear();
    updateEqMarkers();
  };

  //마커 정보 업데이트
  const updateEqMarkers = () => {
    if (Math.floor(zoomLevelRef.current) <= 9) {
      //spec.1~7 (google api. 3~9)
      markerTypeRef.current = 3;
      setEqSingleInfoWindowProps(undefined);
    } else if (Math.floor(zoomLevelRef.current) <= 13) {
      //spec.8~11 (google api. 10~13)
      markerTypeRef.current = 2;
    } else if (Math.floor(zoomLevelRef.current) <= 15) {
      //spec.12~13 (google api. 14~15)
      markerTypeRef.current = 1;
    } else {
      markerTypeRef.current = 0;
    }

    const dataLevel = Math.floor(zoomLevelRef.current / 0.5) * 0.5;
    if (markerTypeRef.current < 3) {
      updateEqSingleMarkers(dataLevel);
    } else {
      updateEqGroupMarkers(dataLevel);
    }
  };

  //핀 마커 정보 업데이트
  const updateEqSingleMarkers = (dataLevel: number) => {
    //const defaultOverlapDist = markerType.current == 0 ? 64.0 : 32.0;
    const defaultOverlapDist = 1; //boundary 검사 안함 (최소 거리만 체크)
    const projection = gMap?.getProjection();
    if (projection) {
      let result = eqSingleMarkerSets.get(dataLevel);
      if (result == null) {
        const eqSingleMarkerSet = MapHelper.makeEqSingleMarkerSet(
          Array.from(markerItemsRef.current.values()),
          projection,
          dataLevel,
          defaultOverlapDist,
        );
        eqSingleMarkerSets.set(dataLevel, eqSingleMarkerSet);
        result = eqSingleMarkerSet;
      }

      if (result) {
        const _eqSingleMarkerProps: EqSingleMarkerProps[] = [];
        for (const row of result.items) {
          if (
            MapUtils.isInBoundary(latlngBoundsRef.current, row.latlng) == true
          ) {
            _eqSingleMarkerProps.push({
              id: row.id,
              latlng: row.latlng,
              eqStat: row.eqStat,
              onClick: handleClickSingleMark,
            });
          }
        }
        setEqSingleMarkerProps(_eqSingleMarkerProps);
        setEqGroupMarkerProps([]);
        return;
      }
    }
    setEqSingleMarkerProps([]);
    setEqGroupMarkerProps([]);
  };

  //그룹 마커 정보 업데이트
  const updateEqGroupMarkers = (dataLevel: number) => {
    const defaultOverlapDist = 80.0;
    const projection = gMap?.getProjection();
    if (projection) {
      let result = eqGroupMarkerSets.get(dataLevel);
      if (result == null) {
        const eqGroupMarkerSet = MapHelper.makeEqGroupMarkerSet(
          Array.from(markerItemsRef.current.values()),
          projection,
          dataLevel,
          defaultOverlapDist,
        );
        eqGroupMarkerSets.set(dataLevel, eqGroupMarkerSet);
        result = eqGroupMarkerSet;
      }

      if (result) {
        const _eqGroupMarkerProps: EqGroupMarkerProps[] = [];
        for (const row of result.items) {
          if (
            MapUtils.isInBoundary(latlngBoundsRef.current, row.latlng) == true
          ) {
            _eqGroupMarkerProps.push(row);
          }
        }
        setEqSingleMarkerProps([]);
        setEqGroupMarkerProps(_eqGroupMarkerProps);
        return;
      }
    }
    setEqSingleMarkerProps([]);
    setEqGroupMarkerProps([]);
  };

  //지도 화면 조정
  const fitBounds = (bounds: google.maps.LatLngBounds) => {
    const padding = {
      top: mapSizeRef.current.height * 0.2,
      right: mapSizeRef.current.width * 0.2,
      bottom: mapSizeRef.current.height * 0.2,
      left: mapSizeRef.current.width * 0.2,
    };
    if (layoutMove == true) {
      const FilteredMapListWidth = 412;
      const width = mapSizeRef.current.width - FilteredMapListWidth;
      padding.right = width * 0.1 + FilteredMapListWidth;
      padding.left = width * 0.1;
    }

    gMap?.fitBounds(bounds, padding);
  };

  return (
    <div className={'w-full h-full'}>
      <GMap
        id={'mmc'}
        maxZoom={maxZoomLevel}
        minZoom={minZoomLevel}
        defaultZoom={defaultLevel}
        onInitMap={handleMapInit}
        onClick={handleMapClick}
        onZoomChanged={handleMapZoomChanged}
        onSizeChanged={handleMapSizeChanged}
        onBoundsChanged={handleMapBoundsChanged}
      >
        {/* single mark */}
        {markerTypeRef.current < 3 &&
          eqSingleMarkerProps.map((data) => {
            return (
              <EqSingleMarker
                key={data.id}
                markerType={markerTypeRef.current}
                {...data}
              />
            );
          })}
        {markerTypeRef.current < 3 && eqSingleInfoWindowProps && (
          <EqMmcInfoWindow {...eqSingleInfoWindowProps} />
        )}

        {/* group mark */}
        {markerTypeRef.current == 3 &&
          eqGroupMarkerProps.map((data) => {
            return <EqGroupMarker key={data.id} {...data} />;
          })}
      </GMap>
      {/* 줌 컨트롤 */}
      <ZoomController
        right={layoutMove ? 'left-[475px]' : 'left-6'}
        bottom={'bottom-12'}
        plus={function (): void {
          const currLevel = gMap?.getZoom() ?? defaultLevel;
          gMap?.setZoom(Math.min(currLevel + 1, maxZoomLevel));
        }}
        minus={function (): void {
          const currLevel = gMap?.getZoom() ?? defaultLevel;
          gMap?.setZoom(Math.max(currLevel - 1, minZoomLevel));
        }}
        min={minZoomLevel}
        max={maxZoomLevel}
        gauge={zoomGauge}
      />
      {/*  필터 */}
      <div className="flex gap-2 absolute top-[100px] right-4">
        <ApplyHistoryPopupButton />
        <StatisticsPopupButton />
      </div>
      {/* <MMCFullScreenPopupButton
        props={{ className: layoutMove ? 'right-[510px]' : '' }}
      /> */}

      <MMCSearchFilter onSearch={handleSearch} />
      <FilteredMapListPopupButton
        status={layoutMove}
        props={{ className: layoutMove ? 'left-[475px]' : '' }}
      />
      {layoutMove && <MMCFilteredMapList />}
    </div>
  );
};

export default Mmc;
