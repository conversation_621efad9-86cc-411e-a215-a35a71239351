﻿import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import SearchItemContainer from '@/Common/Components/SearchItemContainer.tsx';
import SearchLabel from '@/Common/Components/SearchLabel.tsx';
import SearchDropdown from '@/Common/Components/SearchDropDown.tsx';
import Dropdown from '@/Common/Components/DropDown.tsx';
import MMCAlarmGraph from '@/Pages/MMC/components/MMCAlarmGraph.tsx';
import MMCDonutChart from '@/Pages/MMC/components/MMCDonutChart.tsx';
import FilteredItem from '@/Pages/EquipmentDetail/components/Map/FilteredItem.tsx';
import MMCTable from '@/Pages/MMC/components/MMCTable.tsx';
import { ColumnDef } from '@tanstack/react-table';
import CustomColumnHeader from '@/Common/Components/CustomColumnHeader.tsx';
import CustomColumnDataCell from '@/Common/Components/CustomColumnDataCell.tsx';
import { useTable } from '@/Common/Components/hooks/useTable.tsx';
import CustomButton from '@/Common/Components/CustomButton.tsx';
import { MMCResultTable } from '@/types';
import { useTranslation } from 'react-i18next';
import { useRef } from 'react';
import { handleCapture } from '@/Common/function/pdf.ts';

const MMCStatistics = () => {
  const { t } = useTranslation();

  const captureRef = useRef(null);
  const pdfFileName: string = 'Statistics.pdf';

  const columns: ColumnDef<MMCResultTable>[] = [
    {
      accessorKey: 'charger',
      size: 150,
      header: () => (
        <CustomColumnHeader className={'justify-start'}>
          {t('Manager')}
        </CustomColumnHeader>
      ),
      cell: ({ row }) => (
        <CustomColumnDataCell className={'justify-start gap-[10px]'}>
          {row.original.charger}
          <div className="text-center text-[#999999] text-sm font-medium">
            {row.original.id}
          </div>
        </CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'apply',
      size: 56,
      header: () => (
        <CustomColumnHeader>
          {t('Receipt')}({t('Cases')})
        </CustomColumnHeader>
      ),
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.apply}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'done',
      size: 56,
      header: () => (
        <CustomColumnHeader>
          {t('Completed')}({t('Cases')})
        </CustomColumnHeader>
      ),
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.done}</CustomColumnDataCell>
      ),
    },
  ];
  const data = [
    {
      charger: 'Daniel',
      apply: '134',
      done: '82',
      id: 'daniel',
    },
    {
      charger: 'Michael',
      apply: '132',
      done: '80',
      id: 'michael',
    },
    {
      charger: 'James',
      apply: '122',
      done: '85',
      id: 'james',
    },
    {
      charger: 'Sarah',
      apply: '152',
      done: '80',
      id: 'sarah',
    },
    {
      charger: 'Alice',
      apply: '122',
      done: '40',
      id: 'alice',
    },
  ];
  const { table } = useTable(data, columns);

  // 운행 효율 데이터 - 일 제거
  const operationEfficiencyData = [
    { day: '12', value: 97 },
    { day: '13', value: 71 },
    { day: '14', value: 83 },
    { day: '15', value: 88 },
    { day: '16', value: 61 },
    { day: '17', value: 64 },
    { day: '18', value: 33 },
  ];

  // 도넛 차트 데이터 - t() 함수 적용
  const ecoIndexData1 = [
    { name: t('Pending'), value: 35 },
    { name: t('Response'), value: 15 },
    { name: t('CompletedAlarm'), value: 50 },
  ];

  const ecoIndexData2 = [
    { name: t('Due'), value: 35 },
    { name: t('OverdueE'), value: 15 },
    { name: t('CompletedE'), value: 50 },
  ];

  return (
    <CustomFrame className={'mt-10 bg-[#f7f7f7]'} name={t('MMCStatistics')}>
      <section ref={captureRef} className="m-10 mt-7 p-10 bg-white">
        <div className="mb-10 flex items-center justify-between">
          <SearchItemContainer>
            <SearchLabel>{t('Region')}</SearchLabel>
            <SearchDropdown
              options={[]}
              placeholder={t('All')}
              onChange={() => {}}
            />
          </SearchItemContainer>

          <SearchItemContainer>
            <CustomButton
              onClick={() => handleCapture(captureRef, pdfFileName)}
              className="w-fit px-3"
            >
              {t('PDFDownload')}
            </CustomButton>
            <CustomButton className="w-fit px-3">
              {t('SubmissionHistory')}
            </CustomButton>
          </SearchItemContainer>
        </div>

        <div className="grid grid-cols-2 row-auto gap-8 [&_>article]:p-5 [&_>article]:bg-[#F4F4F5] [&_>article]:space-y-8 [&_>article]:rounded-lg [&_>article]:col-span-1">
          <article>
            <SearchItemContainer className={'justify-between'}>
              <SearchLabel>
                {t('FailureAlarmOccurrenceRateByPeriod')}
              </SearchLabel>
              <Dropdown
                options={[]}
                placeholder={t('Monthly')}
                onChange={() => {}}
              />
            </SearchItemContainer>
            <MMCAlarmGraph data={operationEfficiencyData} />
          </article>

          <article>
            <SearchItemContainer className={'justify-between'}>
              <SearchLabel>
                {t('ConsumablesAlarmOccurrenceRateByPeriod')}
              </SearchLabel>
              <Dropdown
                options={[]}
                placeholder={t('Monthly')}
                onChange={() => {}}
              />
            </SearchItemContainer>
            <MMCAlarmGraph data={operationEfficiencyData} />
          </article>

          <article>
            <SearchItemContainer className={'justify-between'}>
              <SearchLabel>{t('FaultCode')}</SearchLabel>
            </SearchItemContainer>

            <SearchItemContainer className={'justify-between'}>
              <MMCDonutChart title={t('Eco Index')} data={ecoIndexData1} />
              <div className="w-[245px] h-[178px] px-[30px] py-6 bg-white rounded-lg flex-col justify-center items-end gap-9 inline-flex">
                <div className="self-stretch h-[27px] flex-col justify-start items-start gap-1 inline-flex">
                  <div className="self-stretch text-lg font-semibold leading-[27px]">
                    {t('FaultResponseDelayRate')}
                  </div>
                </div>
                <div className="self-stretch justify-end items-center gap-1 inline-flex">
                  <div className="text-right text-[56px] font-light">15</div>
                  <div className="text-right text-[32px] font-light">%</div>
                </div>
              </div>
            </SearchItemContainer>
          </article>

          <article>
            <SearchItemContainer className={'justify-between'}>
              <SearchLabel>{t('MaintenanceItem')}</SearchLabel>
            </SearchItemContainer>
            <SearchItemContainer className={'justify-between'}>
              <MMCDonutChart title={t('Eco Index')} data={ecoIndexData2} />
              <div className="w-[245px] h-[178px] px-[30px] py-6 bg-white rounded-lg flex-col justify-center items-end gap-9 inline-flex">
                <div className="self-stretch h-[27px] flex-col justify-start items-start gap-1 inline-flex">
                  <div className="self-stretch text-lg font-semibold leading-[27px]">
                    {t('MaintenanceReplacementDelayRate')}
                  </div>
                </div>
                <div className="self-stretch justify-end items-center gap-1 inline-flex">
                  <div className="text-right text-[56px] font-light">15</div>
                  <div className="text-right text-[32px] font-light">%</div>
                </div>
              </div>
            </SearchItemContainer>
          </article>

          <article>
            <SearchItemContainer className={'justify-between'}>
              <SearchLabel>{t('GazeEquipmentList')}</SearchLabel>
            </SearchItemContainer>
            <div className={'p-2 h-full overflow-auto'}>
              <FilteredItem
                item={{
                  id: 'HHKHFT23JF0000919',
                  machineType: 'E',
                  modelName: 'HXDEMO',
                  hogiNo: '8954',
                  machineNo: 'HHKHFT23JF0000919',
                  custNo: 'HHKHFT23JF0000919',
                  location: 'Broadway, Los Angeles, CA 90015',
                  dealer: 'JR SALES & SERVICE',
                  hourmeter: '8462',
                  eqStat: {
                    idle: false,
                    running: false,
                    consumable: false,
                    broken: false,
                    nogps: false,
                  },
                }}
              ></FilteredItem>
              <FilteredItem
                item={{
                  id: 'HHKHFT23JF0022914',
                  machineType: 'F',
                  modelName: 'HXDEMO',
                  hogiNo: '8957',
                  machineNo: 'HHKHFT23JF0022914',
                  custNo: 'HHKHFT23JF0022914',
                  location: 'Flower St, Los Angeles, CA 90015',
                  dealer: 'JR SALES & SERVICE',
                  hourmeter: '3898',
                  eqStat: {
                    idle: false,
                    running: false,
                    consumable: false,
                    broken: false,
                    nogps: false,
                  },
                }}
              ></FilteredItem>
              <FilteredItem
                item={{
                  id: 'HHKHFT23JF0000910',
                  machineType: 'S',
                  modelName: 'HXDEMO',
                  hogiNo: '8959',
                  machineNo: 'HHKHFT23JF0000910',
                  custNo: 'HHKHFT23JF0000910',
                  location: 'Spring St, Los Angeles, CA 90012',
                  dealer: 'JR SALES & SERVICE',
                  hourmeter: '6666',
                  eqStat: {
                    idle: false,
                    running: false,
                    consumable: false,
                    broken: false,
                    nogps: false,
                  },
                }}
              ></FilteredItem>
              <FilteredItem
                item={{
                  id: 'HHKHFT23JF0000969',
                  machineType: 'L',
                  modelName: 'HXDEMO',
                  hogiNo: '8962',
                  machineNo: 'HHKHFT23JF0000969',
                  custNo: 'HHKHFT23JF0000969',
                  location: '7th St, Los Angeles, CA 90017',
                  dealer: 'JR SALES & SERVICE',
                  hourmeter: '9001',
                  eqStat: {
                    idle: false,
                    running: false,
                    consumable: false,
                    broken: false,
                    nogps: false,
                  },
                }}
              ></FilteredItem>
            </div>
          </article>

          <article>
            <SearchItemContainer className={'justify-between'}>
              <SearchLabel>{t('ResponsePerformance')}</SearchLabel>
            </SearchItemContainer>
            <div className={'h-full'}>
              <MMCTable table={table} columns={columns} />
            </div>
          </article>
        </div>
      </section>
    </CustomFrame>
  );
};

export default MMCStatistics;
