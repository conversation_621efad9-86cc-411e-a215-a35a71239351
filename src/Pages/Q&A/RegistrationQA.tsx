import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { useEffect } from 'react';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import { useToast } from '@/Common/useToast.tsx';
import UseQAPopup from '@/Pages/Q&A/Component/UseQAPopup.tsx';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import CustomButton from '@/Common/Components/CustomButton.tsx';
import SearchLabel from '@/Common/Components/SearchLabel.tsx';
import SearchDropdown from '@/Common/Components/SearchDropDown.tsx';
import SearchItemContainer from '@/Common/Components/SearchItemContainer.tsx';
import Input from '@/Common/Components/Input.tsx';
import { Editor } from '@tinymce/tinymce-react';
import FileDropDown from '@/Common/Components/FileDropDown.tsx';
import TempSavePopup from '@/Pages/Q&A/Component/TempSavePopup.tsx';
import { qnaApi } from '@/api';
import {
  AddQuestionQnaTypeEnum,
  AddQuestionLangTypeEnum,
  QnaInfo,
} from '@/api/generated/index.ts';
import { QnaDetailDTO } from '@/api/generated/src/api/generated/models/qna-detail-dto.ts';
import { ModifyQuestionQnaTypeEnum } from '@/api/generated';
import { MyInfoApi } from '@/api/generated/src/api/generated/api/my-info-api.ts';

const RegistrationQA = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { openWritingPopup } = UseQAPopup();

  const { qnaId } = useParams<{ qnaId: string }>();
  const location = useLocation();
  const editDetail = location.state?.detail as QnaDetailDTO | undefined;

  const [title, setTitle] = useState(editDetail?.title ?? '');
  const [content, setContent] = useState(editDetail?.detail ?? '');
  const [qnaType, setQnaType] = useState<ModifyQuestionQnaTypeEnum | ''>(
    (editDetail?.qnaType as ModifyQuestionQnaTypeEnum) ?? '',
  );
  const [files, setFiles] = useState<File[]>([]);
  const langType =
    i18n.language === 'en'
      ? AddQuestionLangTypeEnum.Us
      : AddQuestionLangTypeEnum.Kr;
  const [isTempPopupOpen, setIsTempPopupOpen] = useState(false);
  const [email, setEmail] = useState(editDetail?.regEmail ?? '');
  const isEditMode = Boolean(qnaId && editDetail);
  const [existingFileNames, setExistingFileNames] = useState<string[]>(
    editDetail?.questionFileName ?? [],
  );

  const handleLoadTempItem = (item: QnaInfo) => {
    setTitle(item.title ?? '');
    setContent(item.detail ?? '');
    setQnaType(item.qnaType ?? '');
  };

  const handleClickTempSavePopup = () => {
    setIsTempPopupOpen(true);
  };

  const options = [
    { key: t('Account'), value: AddQuestionQnaTypeEnum.Account },
    { key: t('ServicePeriod'), value: AddQuestionQnaTypeEnum.Service },
    { key: t('Data'), value: AddQuestionQnaTypeEnum.Data },
    { key: t('Etc'), value: AddQuestionQnaTypeEnum.Other },
  ];

  const handleRegister = async () => {
    try {
      if (!qnaType) return;

      if (isEditMode) {
        await qnaApi.modifyQuestion({
          qnaId: qnaId!,
          qnaType,
          temporary: false,
          title,
          detail: content,
          regEmail: email,
          langType,
          file: files.length ? files : undefined,
        });
      } else {
        await qnaApi.addQuestion({
          qnaType,
          temporary: false,
          title,
          detail: content,
          regEmail: email,
          langType,
          file: files.length ? files : undefined,
        });
      }

      navigate('/qna');
    } catch (error) {
      console.error(isEditMode ? 'Q&A 수정 실패' : 'Q&A 등록 실패', error);
    }
  };

  const handleTempSave = async () => {
    try {
      if (!qnaType) return;

      await qnaApi.addQuestion({
        qnaType,
        temporary: true,
        title,
        detail: content,
        regEmail: email,
        langType,
        file: files.length ? files : undefined,
      });

      toast({
        types: 'success',
        description: t('Savedtemporarily'),
      });
    } catch (error) {
      console.error('Q&A 임시저장 실패:', error);
    }
  };

  const handleFileChange = (newFiles: File[]) => {
    setFiles(newFiles);
  };

  const handleDropdownChange = (field: string, value: string) => {
    if (field === 'qnaType') setQnaType(value as AddQuestionQnaTypeEnum);
  };

  const handleRemoveExistingFileName = (fileName: string) => {
    setExistingFileNames((prev) => prev.filter((f) => f !== fileName));
  };

  const handleBackNavigation = () => {
    openWritingPopup(() => {
      navigate(-1);
    });
  };

  useEffect(() => {
    const fetchEmail = async () => {
      try {
        const myInfoApi = new MyInfoApi(); // config 필요 시 전달
        const response = await myInfoApi.getMyInfoAll();
        setEmail(response.data.personalInfo?.mailAddr || '');
      } catch (error) {
        console.error('', error);
      }
    };

    fetchEmail();
  }, []);

  return (
    <CustomFrame
      name={t('QNARes')}
      back={true}
      onBackClick={handleBackNavigation}
    >
      <section className="px-[76px] pb-10">
        <div className="divider w-[calc(100%+76px)] mt-0 mx-[-36px]"></div>

        <article className="relative w-full">
          <div className="absolute -top-[90px] -right-9 flex gap-2 items-center">
            <CustomButton size="sm" onClick={handleClickTempSavePopup}>
              {t('TemporaryStorage2')}
            </CustomButton>
            <CustomButton
              size="sm"
              onClick={handleTempSave}
              disabled={!title.trim() && !content.trim()}
            >
              {t('Save')}
            </CustomButton>
            <CustomButton
              size="sm"
              disabled={!title.trim() || !content.trim() || !qnaType}
              onClick={handleRegister}
            >
              {t(isEditMode ? 'Edit' : 'Register')}
            </CustomButton>
          </div>
        </article>

        <article className="flex items-center justify-start gap-6">
          <SearchItemContainer>
            <SearchLabel className="min-w-[104px]">
              {t('TypeInquiry')}
            </SearchLabel>
            <SearchDropdown
              selectedKey={qnaType}
              onChange={(value) =>
                handleDropdownChange('qnaType', value.toString())
              }
              options={options}
              placeholder={t('All')}
            />
          </SearchItemContainer>
          <div>
            <span className="body1-b mr-[22px]">{t('Email')}</span>
            <span className="body3-b">{email}</span>
          </div>
        </article>

        <div className="divider my-6"></div>

        <article className="space-y-3">
          <SearchItemContainer className="justify-start gap-5">
            <SearchLabel className="min-w-24">
              {t('Title')}
              <span className="text-error ml-1">*</span>
            </SearchLabel>
            <Input
              placeholder={t('TitleNotice')}
              width="w-full"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
            />
          </SearchItemContainer>
          <SearchItemContainer className="items-start gap-5">
            <SearchLabel className="min-w-24">
              {t('Content')}
              <span className="text-error ml-1">*</span>
            </SearchLabel>
            <Editor
              value={content}
              onEditorChange={(newContent) => setContent(newContent)}
              apiKey="o23x3crldmqswoqnwr5hpx0q4lcc75y4cxei5519iuo7mhbr"
              init={{
                height: 600,
                menubar: false,
                plugins: [
                  'anchor',
                  'autolink',
                  'charmap',
                  'codesample',
                  'link',
                  'lists',
                  'searchreplace',
                  'visualblocks',
                  'wordcount',
                ],
                toolbar:
                  'undo redo | blocks fontsize | bold italic underline strikethrough | link mergetags | addcomment showcomments | spellcheckdialog a11ycheck typography | align lineheight | checklist numlist bullist indent outdent',
                tinycomments_mode: 'embedded',
                tinycomments_author: 'Author name',
              }}
              initialValue=""
            />
          </SearchItemContainer>
          <SearchItemContainer className="items-start gap-5">
            <SearchLabel className="min-w-24">{t('AttatchFile')}</SearchLabel>
            <div className="w-full flex flex-col gap-1">
              <FileDropDown
                onFilesChange={handleFileChange}
                existingFileNames={existingFileNames}
                onRemoveExistingFileName={handleRemoveExistingFileName}
              />
              <div className="text-xs font-semibold text-gray-20 leading-[18px]">
                {t('UpTo5FilesCanBeUploadedJpgJpegBmpPngGifPdf')}
              </div>
            </div>
          </SearchItemContainer>
        </article>

        {isTempPopupOpen && (
          <TempSavePopup
            isOpen={isTempPopupOpen}
            onClose={() => setIsTempPopupOpen(false)}
            onLoad={handleLoadTempItem}
          />
        )}
      </section>
    </CustomFrame>
  );
};

export default RegistrationQA;
