import { useTranslation } from 'react-i18next';
import React from 'react';
import { InfoCardProps } from '@/types';
import user from '@/assets/images/dashboard/user.svg';

/**
 * 충전 및 연비 정보를 보여주는 카드 컴포넌트
 * @param title - 카드 제목
 * @param mainValue - 메인 수치
 * @param mainUnit - 메인 수치의 단위
 * @param subValue - 보조 수치
 * @param subLabel - 보조 수치의 라벨
 * @param className - 추가 스타일링을 위한 클래스
 */
const EnergyEfficiencyCard: React.FC<
  InfoCardProps & { mainUnit: string; subUnit: string; change?: boolean }
> = ({ mainSection, subSection, mainUnit, subUnit }) => {
  const { t, i18n } = useTranslation();
  const isEnglish = i18n.language === 'en';

  return (
    <div className=" flex flex-col justify-between px-[30px] pt-6 pb-[22px] w-full h-full relative bg-white rounded-[8px] shadow-[0px_4px_12px_0px_rgba(0,0,0,0.08)] overflow-hidden">
      <div className="flex items-start justify-between">
        <div>
          <div className="text-lg font-semibold flex justify-between items-center">
            {mainSection.title}
          </div>
          <div className="text-black text-[44px] font-normal flex items-center gap-1 leading-[57.20px]">
            {mainSection.value}
            <span className="text-black text-2xl font-normal leading-[31.20px]">
              {mainUnit}
            </span>
          </div>
        </div>
        <div className="p-[9px] bg-neutral rounded">
          <img src={user} alt="user" />
        </div>
      </div>
      <div className="h-[21px] justify-end items-center flex">
        {isEnglish ? (
          <>
            <span
              className={`caption1-b ${
                mainSection.comparison.isIncrease
                  ? 'text-point-2'
                  : 'text-point-3'
              }`}
            >
              {mainSection.comparison.value}
              {t('HoursH')}
            </span>
            <span className="caption2 text-text-50">
              {mainSection.comparison.isIncrease ? t('Up') : t('Down')}
            </span>
            <span
              className={`caption1-b ${
                mainSection.comparison.isIncrease
                  ? 'text-point-2'
                  : 'text-point-3'
              }`}
            >
              {mainSection.comparison.isIncrease ? '↑' : '↓'}
            </span>
            <span className="caption2 h-5 ml-1 text-text-50 pr-1">
              {mainSection.comparison.text}
            </span>
          </>
        ) : (
          <>
            <span className="caption2 h-5 text-text-50 pr-1">
              {mainSection.comparison.text}
            </span>
            <span className="caption1-b mr-1 text-text-50">
              {mainSection.comparison.value}
              {t('HoursH')}
            </span>
            <span className="caption2 text-text-50">
              {mainSection.comparison.isIncrease ? t('Up') : t('Down')}
            </span>
            <span className="caption1-b text-text-50">
              {mainSection.comparison.isIncrease ? '↑' : '↓'}
            </span>
          </>
        )}
      </div>

      <hr className={'my-3'} />

      <div className="flex items-start justify-between">
        <div>
          <div className="text-lg font-semibold flex justify-between items-center">
            {subSection.title}
          </div>
          <div className="text-black text-[44px] font-normal flex items-center gap-1 leading-[57.20px]">
            {subSection.value}
            <span className="text-black text-2xl font-normal leading-[31.20px]">
              {subUnit}
            </span>
          </div>
        </div>
        <div className="p-[9px] bg-neutral rounded">
          <img src={user} alt="user" />
        </div>
      </div>
      <div className="h-[21px] justify-end items-center flex">
        {isEnglish ? (
          <>
            <span
              className={`caption1-b ${
                subSection.comparison.isIncrease
                  ? 'text-point-2'
                  : 'text-point-3'
              }`}
            >
              {subSection.comparison.value}
              {t('HoursH')}
            </span>
            <span className="caption2 text-text-50">
              {subSection.comparison.isIncrease ? t('Up') : t('Down')}
            </span>
            <span
              className={`caption1-b ${
                subSection.comparison.isIncrease
                  ? 'text-point-2'
                  : 'text-point-3'
              }`}
            >
              {subSection.comparison.isIncrease ? '↑' : '↓'}
            </span>
            <span className="caption2 h-5 ml-1 text-text-50 pr-1">
              {subSection.comparison.text}
            </span>
          </>
        ) : (
          <>
            <span className="caption2 h-5 text-text-50 pr-1">
              {subSection.comparison.text}
            </span>
            <span className="caption1-b mr-1 text-text-50">
              {subSection.comparison.value}
              {t('HoursH')}
            </span>
            <span className="caption2 text-text-50">
              {subSection.comparison.isIncrease ? t('Up') : t('Down')}
            </span>
            <span className="caption1-b text-text-50">
              {subSection.comparison.isIncrease ? '↑' : '↓'}
            </span>
          </>
        )}
      </div>
    </div>
  );
};

export default EnergyEfficiencyCard;
