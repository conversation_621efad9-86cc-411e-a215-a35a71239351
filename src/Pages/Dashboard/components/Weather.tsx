import { useTranslation } from 'react-i18next';
import useDashboardPopup from '@/Pages/Dashboard/useDashboardPopup.tsx';
import { ColumnDef } from '@tanstack/react-table';
import NoticeTable from '@/Pages/Dashboard/components/NoticeTable.tsx';
import temperature_minus from '@/assets/images/temperature-minus.png';
import temperature_plus from '@/assets/images/temperature-plus.png';
import { WeatherProps, WeatherRowProps } from '@/types';
import Clear from '@/assets/images/weather/ic_20_weather_sunny.png';
import Sunny from '@/assets/images/weather/ic_20_weather_sunny.png';
import PartlyCloudy from '@/assets/images/weather/ic_20_weather_partly-cloudy.png';
import Cloudy from '@/assets/images/weather/ic_20_weather_cloudy.png';
import Overcast from '@/assets/images/weather/ic_20_weather_cloudy.png';
import Mist from '@/assets/images/weather/ic_20_weather_fog.png';
import PatchyRainPossible from '@/assets/images/weather/ic_20_weather_partly-rainy.png';
import PatchySnowPossible from '@/assets/images/weather/ic_20_weather_partly-snowy.png';
import PatchySleetPossible from '@/assets/images/weather/ic_20_weather_partly-rainy.png';
import PatchyFreezingDrizzlePossible from '@/assets/images/weather/ic_20_weather_partly-rainy.png';
import ThunderyOutbreaksPossible from '@/assets/images/weather/ic_20_weather_partly-lightning.png';
import BlowingSnow from '@/assets/images/weather/ic_20_weather_snowy-heavy.png';
import Blizzard from '@/assets/images/weather/ic_20_weather_snowy-heavy.png';
import Fog from '@/assets/images/weather/ic_20_weather_fog.png';
import FreezingFog from '@/assets/images/weather/ic_20_weather_fog.png';
import PatchyLightDrizzle from '@/assets/images/weather/ic_20_weather_partly-rainy.png';
import LightDrizzle from '@/assets/images/weather/ic_20_weather_partly-rainy.png';
import FreezingDrizzle from '@/assets/images/weather/ic_20_weather_partly-snowy-rainy.png';
import HeavyFreezingDrizzle from '@/assets/images/weather/ic_20_weather_pouring.png';
import PatchyLightRain from '@/assets/images/weather/ic_20_weather_partly-rainy.png';
import LightRain from '@/assets/images/weather/ic_20_weather_rainy.png';
import ModerateRainAtTimes from '@/assets/images/weather/ic_20_weather_rainy.png';
import ModerateRain from '@/assets/images/weather/ic_20_weather_rainy.png';
import HeavyRainAtTimes from '@/assets/images/weather/ic_20_weather_pouring.png';
import HeavyRain from '@/assets/images/weather/ic_20_weather_pouring.png';
import LightFreezingRain from '@/assets/images/weather/ic_20_weather_partly-snowy-rainy.png';
import ModerateOrHeavyFreezingRain from '@/assets/images/weather/ic_20_weather_pouring.png';
import LightSleet from '@/assets/images/weather/ic_20_weather_partly-snowy-rainy.png';
import ModerateOrHeavySleet from '@/assets/images/weather/ic_20_weather_partly-snowy-rainy.png';
import PatchyLightSnow from '@/assets/images/weather/ic_20_weather_partly-snowy.png';
import LightSnow from '@/assets/images/weather/ic_20_weather_snowy.png';
import PatchyModerateSnow from '@/assets/images/weather/ic_20_weather_snowy.png';
import ModerateSnow from '@/assets/images/weather/ic_20_weather_snowy.png';
import PatchyHeavySnow from '@/assets/images/weather/ic_20_weather_snowy-heavy.png';
import HeavySnow from '@/assets/images/weather/ic_20_weather_snowy-heavy.png';
import IcePellets from '@/assets/images/weather/ic_20_weather_hail.png';
import LightRainShower from '@/assets/images/weather/ic_20_weather_partly-rainy.png';
import ModerateOrHeavyRainShower from '@/assets/images/weather/ic_20_weather_pouring.png';
import TorrentialRainShower from '@/assets/images/weather/ic_20_weather_pouring.png';
import LightSleetShowers from '@/assets/images/weather/ic_20_weather_partly-snowy-rainy.png';
import ModerateOrHeavySleetShowers from '@/assets/images/weather/ic_20_weather_partly-snowy-rainy.png';
import LightSnowShowers from '@/assets/images/weather/ic_20_weather_snowy.png';
import ModerateOrHeavySnowShowers from '@/assets/images/weather/ic_20_weather_snowy-heavy.png';
import PatchyLightRainWithThunder from '@/assets/images/weather/ic_20_weather_partly-lightning.png';
import ModerateOrHeavyRainWithThunder from '@/assets/images/weather/ic_20_weather_lightning-rainy.png';
import PatchyLightSnowWithThunder from '@/assets/images/weather/ic_20_weather_partly-lightning.png';
import ModerateOrHeavySnowWithThunder from '@/assets/images/weather/ic_20_weather_partly-lightning.png';

//const weatherStatus = [hail, cloudy, pouring, sunny, rainy];
const warnStatus = [temperature_minus, temperature_plus, null];

export enum WeatherType {
  Hail = 0,
  Cloudy = 1,
  Pouring = 2,
  Sunny = 3,
  Rainy = 4,
}

const weatherConditions = [
  { name: 'Clear', path: Clear },
  { name: 'Sunny', path: Sunny },
  { name: 'Partly cloudy', path: PartlyCloudy },
  { name: 'Cloudy', path: Cloudy },
  { name: 'Overcast', path: Overcast },
  { name: 'Mist', path: Mist },
  { name: 'Patchy rain possible', path: PatchyRainPossible },
  { name: 'Patchy snow possible', path: PatchySnowPossible },
  { name: 'Patchy sleet possible', path: PatchySleetPossible },
  {
    name: 'Patchy freezing drizzle possible',
    path: PatchyFreezingDrizzlePossible,
  },
  { name: 'Thundery outbreaks possible', path: ThunderyOutbreaksPossible },
  { name: 'Blowing snow', path: BlowingSnow },
  { name: 'Blizzard', path: Blizzard },
  { name: 'Fog', path: Fog },
  { name: 'Freezing fog', path: FreezingFog },
  { name: 'Patchy light drizzle', path: PatchyLightDrizzle },
  { name: 'Light drizzle', path: LightDrizzle },
  { name: 'Freezing drizzle', path: FreezingDrizzle },
  { name: 'Heavy freezing drizzle', path: HeavyFreezingDrizzle },
  { name: 'Patchy light rain', path: PatchyLightRain },
  { name: 'Light rain', path: LightRain },
  { name: 'Moderate rain at times', path: ModerateRainAtTimes },
  { name: 'Moderate rain', path: ModerateRain },
  { name: 'Heavy rain at times', path: HeavyRainAtTimes },
  { name: 'Heavy rain', path: HeavyRain },
  { name: 'Light freezing rain', path: LightFreezingRain },
  {
    name: 'Moderate or heavy freezing rain',
    path: ModerateOrHeavyFreezingRain,
  },
  { name: 'Light sleet', path: LightSleet },
  { name: 'Moderate or heavy sleet', path: ModerateOrHeavySleet },
  { name: 'Patchy light snow', path: PatchyLightSnow },
  { name: 'Light snow', path: LightSnow },
  { name: 'Patchy moderate snow', path: PatchyModerateSnow },
  { name: 'Moderate snow', path: ModerateSnow },
  { name: 'Patchy heavy snow', path: PatchyHeavySnow },
  { name: 'Heavy snow', path: HeavySnow },
  { name: 'Ice pellets', path: IcePellets },
  { name: 'Light rain shower', path: LightRainShower },
  { name: 'Moderate or heavy rain shower', path: ModerateOrHeavyRainShower },
  { name: 'Torrential rain shower', path: TorrentialRainShower },
  { name: 'Light sleet showers', path: LightSleetShowers },
  {
    name: 'Moderate or heavy sleet showers',
    path: ModerateOrHeavySleetShowers,
  },
  { name: 'Light snow showers', path: LightSnowShowers },
  { name: 'Moderate or heavy snow showers', path: ModerateOrHeavySnowShowers },
  { name: 'Patchy light rain with thunder', path: PatchyLightRainWithThunder },
  {
    name: 'Moderate or heavy rain with thunder',
    path: ModerateOrHeavyRainWithThunder,
  },
  { name: 'Patchy light snow with thunder', path: PatchyLightSnowWithThunder },
  {
    name: 'Moderate or heavy snow with thunder',
    path: ModerateOrHeavySnowWithThunder,
  },
];

const Weather = ({ weather }: WeatherProps) => {
  const { t } = useTranslation();

  const { openTemperatureSettingPopup } = useDashboardPopup();

  const columns: ColumnDef<WeatherRowProps>[] = [
    {
      accessorKey: 'fleet',
      header: t('FleetNameN'),
      maxSize: 168,
      size: 168,
      cell: ({ row }) => <div className={''}>{row.original.fleet}</div>,
    },
    {
      accessorKey: 'location',
      header: t('Location'),
      maxSize: 324,
      size: 324,
      cell: ({ row }) => <div className={''}>{row.original.location}</div>,
    },
    {
      accessorKey: 'weatherDesc',
      header: t('Weather'),
      maxSize: 120,
      size: 120,
      cell: ({ row }) => {
        // `filter`를 사용하여 weatherDesc와 일치하는 객체 찾기
        const matchedWeather = weatherConditions.filter(
          (item) => item.name === row.original.weatherDesc,
        )?.[0];

        // `matchedWeather`가 있으면 이미지 경로를 사용하고, 없으면 null
        const imagePath = matchedWeather ? matchedWeather.path : null;

        return (
          <div className={'flex justify-center items-center gap-2'}>
            {/* 이미지 경로가 있을 경우만 이미지 태그 렌더링 */}
            {imagePath && (
              <img src={imagePath} alt={row.original.weatherDesc} />
            )}
            {/* 날씨 설명과 온도 출력 */}
            {row.original.weatherDesc} {row.original.tempF}°F
          </div>
        );
      },
    },
    {
      accessorKey: 'warn',
      header: t('Warning'),
      maxSize: 120,
      size: 120,
      cell: ({ row }) => {
        const src = warnStatus[row.original.warn];
        if (src) {
          return (
            <div className={'flex justify-center items-center gap-1'}>
              <img src={src} />
            </div>
          );
        } else {
          return (
            <div className={'flex justify-center items-center gap-1'}>
              {'-'}
            </div>
          );
        }
      },
    },
  ];

  return (
    <div className="w-full h-full relative bg-white rounded-[8px] shadow-[0px_4px_12px_0px_rgba(0,0,0,0.08)]">
      <div className={'p-[30px] space-y-[30px] h-full'}>
        <div className="w-full flex justify-between items-center text-lg font-semibold leading-[27px]">
          <span>{t('WeatherInformation')}</span>
          <div
            onClick={openTemperatureSettingPopup}
            className="text-right text-[#646363] text-base font-normal underline leading-normal cursor-pointer"
          >
            {t('SetTemperatureCriteria')}
          </div>
        </div>
        <div className={'overflow-y-auto h-[calc(100%-60px)]'}>
          <NoticeTable<WeatherRowProps> columns={columns} data={weather} />
        </div>
      </div>
    </div>
  );
};

export default Weather;
