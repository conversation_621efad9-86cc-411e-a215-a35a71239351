import CustomButton from '@/Common/Components/CustomButton.tsx';
import SearchDropdown from '@/Common/Components/SearchDropDown.tsx';
import SearchItemContainer from '@/Common/Components/SearchItemContainer.tsx';
import SearchLabel from '@/Common/Components/SearchLabel.tsx';
import useDimension from '@/hooks/useDimension.tsx';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import useStatisticsPopup from '@/Pages/Statistics/components/popup/useStatisticsPopup.tsx';
import AverageWorkTime from '@/Pages/Statistics/components/ProductivityEfficiency/AverageWorkTime.tsx';
import EqOperationRate from '@/Pages/Statistics/components/ProductivityEfficiency/EQOperationRate.tsx';
import JobRestTime from '@/Pages/Statistics/components/ProductivityEfficiency/JobRestTime.tsx';
import StatModelWorkTimeRanking from '@/Pages/Statistics/components/ProductivityEfficiency/StatModelWorkTimeRanking.tsx';
import WorkerRanking from '@/Pages/Statistics/components/ProductivityEfficiency/WorkerRanking.tsx';
import WorkerRestTimeRanking from '@/Pages/Statistics/components/ProductivityEfficiency/WorkerRestTimeRanking.tsx';
import WorkTimeRanking from '@/Pages/Statistics/components/ProductivityEfficiency/WorkTimeRanking.tsx';
import { Responsive as ResponsiveGridLayout } from 'react-grid-layout';
import { useTranslation } from 'react-i18next';
import { useEffect, useMemo, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { fleetManagementApi, statProductivityEfficiencyApi } from '@/api';
import { fleetSelectItem } from '@/types';
import {
  FleetDTO,
  type StatMachineWorkingRankDTO,
  type StatModelWorkingRankDTO,
  StatProductivityEfficiencyApiGetFleetNoRunMonthDataRequest,
  StatProductivityEfficiencyApiGetMachineMonthWorkTimeRankingRequest,
  StatProductivityEfficiencyApiGetModelMonthWorkTimeRankingRequest,
} from '@/api/generated';
import StatMachineNoWorkTimeRanking from '@/Pages/Statistics/components/ProductivityEfficiency/StatMachineNoWorkTimeRanking.tsx';

const ProductivityEfficiency = () => {
  const { t } = useTranslation();

  const [inputValues, setInputValues] = useState({
    fleetSeqNo: 'ALL',
  });

  // 드롭다운 변경 핸들러
  const handleDropdownChange = (field: string, value: string) => {
    console.log(value);
    setInputValues((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  //플릿 drop list 만들기 위한 api
  const { data: fleetEquipmentData } = useQuery({
    queryKey: ['get-prod-effi-down-fleet-dropdown'],
    queryFn: async () => {
      const response = await fleetManagementApi.getFleetList({
        searchName: undefined,
      });
      const fleetSelectData: fleetSelectItem[] = [
        { key: '전체', value: 'ALL' },
      ];

      fleetSelectData.push(
        ...response.data.map((item: FleetDTO) => ({
          key: item.name ?? '',
          value: item.seqNo?.toString() ?? 'ALL',
        })),
      );

      return fleetSelectData;
    },
    enabled: true,
  });

  ////////////////////////////////////////////////
  // (장비 사용 시간 순위 : 월 정의함.)
  ///////////////////////////////////////////////
  const [requestWorkTimeParams, setRequestWorkTimeParams] =
    useState<StatProductivityEfficiencyApiGetFleetNoRunMonthDataRequest>({
      //TODO: 이전 달 정의 해야함.
      month: '202502',
      seqNo: 'ALL',
    });

  useEffect(() => {
    setRequestWorkTimeParams((prev) => ({
      ...prev,
      seqNo: inputValues.fleetSeqNo ?? 'ALL',
    }));
  }, [inputValues]);

  const { data: statWorkTimeData } = useQuery({
    queryKey: ['get-stat-work-time-hour', requestWorkTimeParams],
    queryFn: async () => {
      const response =
        await statProductivityEfficiencyApi.getFleetNoRunMonthData(
          requestModelParams,
        );
      console.log(response);
      if (Array.isArray(response.data)) {
        return response.data;
      } else {
        return [];
      }
    },
    enabled: true,
  });

  // //장비 가동률 데이터

  const ecoIndexData = useMemo(
    () => {
      // 배열이 비어있거나 인덱스 1이 없는 경우 기본값 처리
      if (!statWorkTimeData || statWorkTimeData.length <= 1) {
        return [
          { name: t('WorkingE'), value: 0 },
          { name: t('NonOperationS'), value: 0 },
          { name: t('Repair'), value: 0 },
        ];
      }
      return [
        { name: t('WorkingE'), value: Number(statWorkTimeData[1].workingRate) },
        {
          name: t('NonOperationS'),
          value: Number(statWorkTimeData[1].travelRate),
        },
        { name: t('Repair'), value: Number(statWorkTimeData[1].idleRate) },
      ];
    },
    [statWorkTimeData, t], // t 함수도 의존성에 추가
  );

  ////////////////////////////////////////////////
  // (모델별 작업시간 순위 : 월 정의함.)
  ///////////////////////////////////////////////
  const [requestModelParams, setRequestModelParams] =
    useState<StatProductivityEfficiencyApiGetModelMonthWorkTimeRankingRequest>({
      //TODO: 이전 달 정의 해야함.
      month: '202502',
      seqNo: 'ALL',
    });

  useEffect(() => {
    setRequestModelParams((prev) => ({
      ...prev,
      seqNo: inputValues.fleetSeqNo ?? 'ALL',
    }));
  }, [inputValues]);

  const { data: statModelWorkTimeRankingData } = useQuery({
    queryKey: ['get-stat-model-month-work-hour-rank', requestModelParams],
    queryFn: async () => {
      const response =
        await statProductivityEfficiencyApi.getModelMonthWorkTimeRanking(
          requestModelParams,
        );
      console.log('모델별 작업시간 응답', response);
      if (Array.isArray(response.data)) {
        return response.data.map((item: StatModelWorkingRankDTO) => ({
          name: item?.smodel || '',
          hitNumber: Number(item.workingHour),
          upDown: true,
          increase: 1,
        }));
      } else {
        return [];
      }
    },
    enabled: true,
  });

  ////////////////////////////////////////////////

  ////////////////////////////////////////////////
  // (장비별 작업시간 순위 : 월 정의함.)
  ///////////////////////////////////////////////
  const [requestMachineNoParams, setRequestMachineNoParams] =
    useState<StatProductivityEfficiencyApiGetMachineMonthWorkTimeRankingRequest>(
      {
        //TODO: 이전 달 정의 해야함.
        month: '202502',
        seqNo: 'ALL',
      },
    );

  useEffect(() => {
    setRequestMachineNoParams((prev) => ({
      ...prev,
      seqNo: inputValues.fleetSeqNo ?? 'ALL',
    }));
  }, [inputValues]);

  const { data: statMachineWorkTimeRankingData } = useQuery({
    queryKey: ['get-stat-machine-month-work-hour-rank', requestMachineNoParams],
    queryFn: async () => {
      const response =
        await statProductivityEfficiencyApi.getMachineMonthWorkTimeRanking(
          requestMachineNoParams,
        );
      console.log(response);
      if (Array.isArray(response.data)) {
        return response.data.map((item: StatMachineWorkingRankDTO) => ({
          name: item?.model || '',
          hitNumber: Number(item.workingHour),
          hogi: item?.hogi || '',
          upDown: true,
          increase: 1,
        }));
      } else {
        return [];
      }
    },
    enabled: true,
  });
  ////////////////////////////////////////////////

  // 각 화면 크기별 레이아웃 정의
  // lg: 데스크탑(4열), md: 태블릿(2열), sm: 작은 태블릿(1열), xs: 모바일(1열)
  const layouts = {
    lg: [
      { i: 'a', x: 0, y: 0, w: 1, h: 30, static: false }, // EqOperationRate (높이 증가: 30 -> 35)
      { i: 'b', x: 1, y: 0, w: 1, h: 30, static: false }, // AverageWorkTime (높이 증가: 30 -> 35)
      { i: 'c', x: 2, y: 0, w: 2, h: 20, static: false }, // WorkTimeRanking
      { i: 'd', x: 2, y: 1, w: 2, h: 10, static: false }, // JobRestTime (높이 감소: 20 -> 15)
      { i: 'e', x: 0, y: 3, w: 2, h: 20, static: false }, // StatModelWorkTimeRanking
      { i: 'f', x: 2, y: 3, w: 2, h: 20, static: false }, // EqWorkTimeRanking
      { i: 'g', x: 0, y: 4, w: 2, h: 20, static: false }, // WorkerRanking
      { i: 'h', x: 2, y: 4, w: 2, h: 20, static: false }, // WorkerRestTimeRanking
    ],
    md: [
      { i: 'a', x: 0, y: 0, w: 1, h: 35, static: false }, // EqOperationRate (높이 증가)
      { i: 'b', x: 1, y: 0, w: 1, h: 35, static: false }, // AverageWorkTime (높이 증가)
      { i: 'c', x: 0, y: 1, w: 2, h: 20, static: false }, // WorkTimeRanking
      { i: 'd', x: 0, y: 2, w: 2, h: 15, static: false }, // JobRestTime (높이 감소)
      { i: 'e', x: 0, y: 3, w: 2, h: 20, static: false }, // StatModelWorkTimeRanking
      { i: 'f', x: 0, y: 4, w: 2, h: 20, static: false }, // EqWorkTimeRanking
      { i: 'g', x: 0, y: 5, w: 2, h: 20, static: false },
      { i: 'h', x: 0, y: 6, w: 2, h: 20, static: false },
    ],
    sm: [
      { i: 'a', x: 0, y: 0, w: 1, h: 35, static: false }, // 높이 증가
      { i: 'b', x: 0, y: 1, w: 1, h: 35, static: false }, // 높이 증가
      { i: 'c', x: 0, y: 2, w: 1, h: 20, static: false }, // 그대로 유지
      { i: 'd', x: 0, y: 3, w: 1, h: 15, static: false }, // 높이 감소
      { i: 'e', x: 0, y: 4, w: 1, h: 20, static: false }, // 그대로 유지
      { i: 'f', x: 0, y: 5, w: 1, h: 20, static: false }, // 그대로 유지
      { i: 'g', x: 0, y: 6, w: 1, h: 20, static: false }, // 그대로 유지
      { i: 'h', x: 0, y: 7, w: 1, h: 20, static: false }, // 그대로 유지
    ],
    xs: [
      { i: 'a', x: 0, y: 0, w: 1, h: 35, static: false }, // 높이 증가
      { i: 'b', x: 0, y: 1, w: 1, h: 35, static: false }, // 높이 증가
      { i: 'c', x: 0, y: 2, w: 1, h: 20, static: false }, // 그대로 유지
      { i: 'd', x: 0, y: 3, w: 1, h: 15, static: false }, // 높이 감소
      { i: 'e', x: 0, y: 4, w: 1, h: 20, static: false }, // 그대로 유지
      { i: 'f', x: 0, y: 5, w: 1, h: 20, static: false }, // 그대로 유지
      { i: 'g', x: 0, y: 6, w: 1, h: 20, static: false }, // 그대로 유지
      { i: 'h', x: 0, y: 7, w: 1, h: 20, static: false }, // 그대로 유지
    ],
  };

  // 브레이크포인트 설정
  const breakpoints = { lg: 1200, md: 996, sm: 768, xs: 480 };
  const cols = { lg: 4, md: 2, sm: 1, xs: 1 };

  const { width } = useDimension();
  const gridWidth = width > 1024 ? width - 280 : width - 72;

  const { openWorkerEqStatisticsPopup } = useStatisticsPopup();

  return (
    <CustomFrame name={t('ProductivityEfficiencyAnalysis')}>
      <div className={'px-10 py-3 space-y-8'}>
        <SearchItemContainer className={'justify-between'}>
          <SearchItemContainer>
            <SearchLabel>{t('Fleet')}</SearchLabel>
            <SearchDropdown
              onChange={(value) =>
                handleDropdownChange('fleetSeqNo', value.toString())
              }
              options={fleetEquipmentData || []}
              placeholder={t('All')}
            />
          </SearchItemContainer>
          <CustomButton
            onClick={openWorkerEqStatisticsPopup}
            variant={'orange2'}
          >
            {t('Download')}
          </CustomButton>
        </SearchItemContainer>
      </div>
      <div style={{ overflowX: 'auto', width: '100%' }}>
        <div style={{ minWidth: '1440px' }}>
          <ResponsiveGridLayout
            className="layout px-4"
            layouts={layouts}
            breakpoints={breakpoints}
            cols={cols}
            isDroppable={false}
            isDraggable={false}
            rowHeight={1}
            width={gridWidth}
            margin={[16, 16]}
            containerPadding={[16, 16]}
          >
            <div className={'p-2'} key="a">
              <EqOperationRate
                title={t('MachineUtilizationRate')}
                data={ecoIndexData}
              />
            </div>
            <div className={'p-2'} key="b">
              <AverageWorkTime title={t('AverageWorkingHours')} data={[]} />
            </div>
            <div className={'p-2'} key={'c'}>
              {/*<BreakdownStatistics />*/}
              <WorkTimeRanking workData={statWorkTimeData ?? []} />
            </div>

            <div className={'p-2'} key={'d'}>
              {/*<ExpendableStatistics />*/}
              <JobRestTime workData={statWorkTimeData ?? []} />
            </div>
            <div className={'p-2'} key={'e'}>
              <StatModelWorkTimeRanking
                eq={statModelWorkTimeRankingData ?? []}
              />
            </div>
            <div className={'p-2'} key={'f'}>
              <StatMachineNoWorkTimeRanking
                eq={statMachineWorkTimeRankingData ?? []}
              />
            </div>
            <div className={'p-2'} key={'g'}>
              {/*<FMEAChart title={'고장 심각도별 순위'} data={[]} />*/}
              <WorkerRanking eq={[]} />
            </div>
            <div className={'p-2'} key={'h'}>
              {/*<BreakdownLineGraph title={'소모품 교체 지연률'} data={[]} />*/}
              <WorkerRestTimeRanking eq={[]} />
            </div>
          </ResponsiveGridLayout>
        </div>
      </div>
    </CustomFrame>
  );
};

export default ProductivityEfficiency;
