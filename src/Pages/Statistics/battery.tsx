import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import { Tabs } from '@radix-ui/themes';
import BatteryConsumption from '@/Pages/Statistics/components/battery/BatteryConsumption.tsx';
import LithiumBattery from '@/Pages/Statistics/components/battery/LithiumBattery.tsx';
import ModelChargeCount from '@/Pages/Statistics/components/battery/ModelChargeCount.tsx';

const Battery = () => {
  const { t } = useTranslation();

  const [value, setValue] = useState('ChargingCountByModel');
  return (
    <CustomFrame name={t('BatteryAnalysis')}>
      <Tabs.Root value={value} onValueChange={setValue}>
        <Tabs.List className={'tab-design px-8'}>
          <Tabs.Trigger value={'ChargingCountByModel'}>
            <span>{t('ChargingCountByModel')}</span>
          </Tabs.Trigger>
          <Tabs.Trigger value={'BatteryConsumption'}>
            <span>{t('BatteryConsumption')}</span>
          </Tabs.Trigger>
          <Tabs.Trigger value={'LithiumBattery'}>
            <span>{t('LithiumBattery')}</span>
          </Tabs.Trigger>
        </Tabs.List>
        <ModelChargeCount />
        <BatteryConsumption />
        <LithiumBattery />
      </Tabs.Root>
    </CustomFrame>
  );
};

export default Battery;
