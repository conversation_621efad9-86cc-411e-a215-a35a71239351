import { useTranslation } from 'react-i18next';
import { v4 } from 'uuid';
import { ExpendableStatisticsProps, RowStatProps } from '@/types';
import React, { useEffect, useState } from 'react';
import {
  type StatFaultCountByModelDTO,
  StatisticsFaultApiGetDayFaultCountByModelRequest,
} from '@/api/generated';
import { formatDate } from '@/Common/function/date.ts';
import { useQuery } from '@tanstack/react-query';
import { statisticsFaultApi } from '@/api';
import arrow_drop_down from '@/assets/images/Arrow_drop_down.png';
import arrow_drop_up from '@/assets/images/Arrow_drop_up.png';
import arrow_eq from '@/assets/images/Arrow_eq.svg';

const ModelBreakdownAlarm: React.FC<ExpendableStatisticsProps> = ({
  fleetValue,
}) => {
  const { t } = useTranslation();

  //TODO: 오늘 날짜 설정 해야함.
  const today = new Date('2024-03-02T00:00:00');
  const yesterday = new Date('2024-03-01T00:00:00');

  // fleetValue가 변경될 때마다 requestParams 업데이트
  useEffect(() => {
    setRequestTodayParams((prev) => ({
      ...prev,
      fleetSeqNo: fleetValue === 'ALL' ? undefined : Number(fleetValue),
    }));
    setRequestYesterdayParams((prev) => ({
      ...prev,
      fleetSeqNo: fleetValue === 'ALL' ? undefined : Number(fleetValue),
    }));
  }, [fleetValue]);

  //오늘 날짜
  const [requestTodayParams, setRequestTodayParams] =
    useState<StatisticsFaultApiGetDayFaultCountByModelRequest>({
      pageNum: 1,
      pageSize: 5,
      date: formatDate(today),
      fleetSeqNo: fleetValue === 'ALL' ? undefined : Number(fleetValue),
    });

  const { data: statBreakTodayAlarmData } = useQuery({
    queryKey: ['get-stat-today-fault-model-count', requestTodayParams],
    queryFn: async () => {
      const response =
        await statisticsFaultApi.getDayFaultCountByModel(requestTodayParams);
      console.log(response);
      if (Array.isArray(response.data)) {
        return response.data.map((item: StatFaultCountByModelDTO) => ({
          name: item?.smodel || '',
          hitNumber: Number(item.cnt),
          upDown: true,
        }));
      } else {
        return [];
      }
    },
    enabled: true,
  });

  //어제 날짜
  const [requestYeasterdayParams, setRequestYesterdayParams] =
    useState<StatisticsFaultApiGetDayFaultCountByModelRequest>({
      pageNum: 1,
      pageSize: 5,
      date: formatDate(yesterday),
      fleetSeqNo: fleetValue === 'ALL' ? undefined : Number(fleetValue),
    });

  const { data: statBreakYesterdayAlarmData } = useQuery({
    queryKey: ['get-stat-yesterday-fault-model-count', requestYeasterdayParams],
    queryFn: async () => {
      const response = await statisticsFaultApi.getDayFaultCountByModel(
        requestYeasterdayParams,
      );
      console.log(response);
      if (Array.isArray(response.data)) {
        return response.data.map((item: StatFaultCountByModelDTO) => ({
          name: item?.smodel || '',
          hitNumber: Number(item.cnt),
          upDown: 0,
        }));
      } else {
        return [];
      }
    },
    enabled: true,
  });

  const DUMMY_ROWS = [
    { name: '160D-9', hitNumber: 23, upDown: true, increase: 1, rowNum: 1 },
    { name: '110D-9', hitNumber: 18, upDown: true, increase: 3, rowNum: 2 },
    { name: '80D-9', hitNumber: 15, upDown: true, increase: -1, rowNum: 3 },
    { name: '250D-9', hitNumber: 10, upDown: true, increase: 1, rowNum: 4 },
    { name: '70D-9', hitNumber: 7, upDown: true, increase: 4, rowNum: 5 },
  ];

  const rowData = () => {
    if (
      Array.isArray(statBreakTodayAlarmData) &&
      Array.isArray(statBreakYesterdayAlarmData) &&
      statBreakTodayAlarmData.length > 0 &&
      statBreakYesterdayAlarmData.length > 0
    ) {
      return statBreakTodayAlarmData.map((item, index) => ({
        name: item.name,
        hitNumber: item.hitNumber,
        upDown: true,
        increase: findUpDown(item.name, index),
        rowNum: index + 1,
      }));
    } else {
      return DUMMY_ROWS;
    }
  };

  const findUpDown = (name: string, index: number) => {
    if (Array.isArray(statBreakYesterdayAlarmData)) {
      const indexOf = statBreakYesterdayAlarmData.findIndex(
        (item) => item.name === name,
      );
      if (indexOf === -1) return 1;
      else if (indexOf > index) return -1;
      else return 0;
    } else return 1;
  };

  return (
    <div className="w-full h-full flex relative bg-white rounded-[8px] shadow-[0px_4px_12px_0px_rgba(0,0,0,0.08)]">
      <div className={'flex-1 p-[30px] space-y-[30px]'}>
        <div className="text-lg font-semibold leading-[27px]">
          {t('FaultAlarmRankingByModel')}
        </div>
        <div className={'space-y-3 overflow-y-auto h-[calc(100%-60px)]'}>
          {rowData() &&
            rowData().map((item, index) => (
              <Row
                key={v4()}
                name={item.name}
                hitNumber={item.hitNumber}
                upDown={item.upDown}
                increase={item.increase}
                rowNum={index + 1}
              />
            ))}
        </div>
      </div>
    </div>
  );
};

const Row = ({ rowNum, name, hitNumber, increase }: RowStatProps) => {
  const { t } = useTranslation();

  return (
    <div>
      <div className="h-3.5 mb-3 flex justify-between items-center px-9">
        <div className="h-6 flex items-center gap-2 overflow-hidden">
          <div className="w-[15px] text-black text-base font-bold">
            {rowNum}.
          </div>
          <div className="text-base font-medium">{name}</div>
          <div className="text-text-50 body3-n [&_em]:text-text-50 [&_em]:ml-1">
            {hitNumber}
            <em>{t('Hours')}</em>
          </div>
        </div>
        <div>
          {increase === 1 ? (
            <img src={arrow_drop_up} className="w-6 h-6 relative" />
          ) : increase === 0 ? (
            <img src={arrow_eq} className="w-6 h-6 relative" />
          ) : (
            <img src={arrow_drop_down} className="w-6 h-6 relative" />
          )}
        </div>
      </div>
      <hr />
    </div>
  );
};

export default ModelBreakdownAlarm;
