import { useTranslation } from 'react-i18next';
import { AlertPopupProps } from '@/types';
import Layout from '@/Common/Popup/Layout.tsx';
import close_popup from '@/assets/images/close_popup.png';
import SearchItemContainer from '@/Common/Components/SearchItemContainer.tsx';
import SearchLabel from '@/Common/Components/SearchLabel.tsx';
import CustomButton from '@/Common/Components/CustomButton.tsx';
import { useMutation, useQuery } from '@tanstack/react-query';
import { calendarApi } from '@/api';
import {
  CalendarApiDeleteCalendarRequest,
  CalendarApiGetCalendarDetailDataRequest,
} from '@/api/generated';
import { calendarTextColor } from '@/Common/function/utils.ts';
import SearchHtmlLabel from '@/Common/Components/SearchHtmlLabel.tsx';
import { useState } from 'react';
import { toast } from '@/Common/useToast.tsx';
import UseSchedulePopup from '@/Pages/Calendar/components/useSchedulePopup.tsx';

const ScheduleInfoPopup = ({
  isOpen,
  // buttonText,
  // secondButtonText,
  onClose,
  onConfirm,
  calendarId,
}: AlertPopupProps) => {
  const { t } = useTranslation();

  const { openScheduleAddPopup } = UseSchedulePopup();

  const params: CalendarApiGetCalendarDetailDataRequest = {
    calendarId: Number(calendarId),
  };
  const { data: calendarInfo } = useQuery({
    queryKey: ['calendar-info-data', params],
    queryFn: async () => {
      try {
        console.log(calendarId);
        const respone = await calendarApi.getCalendarDetailData(params);
        console.log(respone.data);
        return respone.data;
      } catch (e) {
        console.log(e);
      }
    },
    enabled: true,
  });

  const [bodyRequest] = useState<CalendarApiDeleteCalendarRequest>({
    calendarId: Number(calendarId),
  });

  const createFleetMutation = useMutation({
    mutationFn: (bodyRequest: CalendarApiDeleteCalendarRequest) => {
      return calendarApi.deleteCalendar(bodyRequest);
    },
    onSuccess: () => {
      console.log('refresh 하면 됨.');
      onClose();
      toast({ types: 'success', description: t('DeletingSchedule') });
      onConfirm();
    },
    onError: (error) => {
      console.error('API 호출 에러:', error);
    },
  });

  const repeatStr = (type?: string) => {
    switch (type) {
      case 'N':
        return t('N');
      case 'DAILY':
        return t('DAILY');
      case 'WEEKLY':
        return t('WEEKLY');
      case 'MONTHLY':
        return t('MONTHLY');
      case 'YEARLY':
        return t('YEARLY');
      default:
        return t('N');
    }
  };

  return (
    <Layout isOpen={isOpen}>
      <div className="w-[720px] bg-white rounded">
        <div className="self-stretch p-10 rounded-lg justify-between items-center flex">
          <div className="self-stretch justify-start items-center gap-2 inline-flex">
            <div className="text-2xl font-bold leading-normal">
              {t('NotificationDetails')}
            </div>
          </div>
          <img
            src={close_popup}
            className="w-6 h-6 cursor-pointer"
            onClick={onClose}
          />
        </div>
        <div className={'px-10 w-full space-y-6'}>
          <div className={'space-y-6'}>
            <SearchItemContainer className={'justify-start'}>
              <div
                className="w-4 h-4 rounded-md"
                style={{
                  backgroundColor: calendarTextColor(calendarInfo?.colorType),
                }}
              />
              <SearchLabel>{calendarInfo?.title}</SearchLabel>
            </SearchItemContainer>
            <SearchItemContainer className={'justify-start'}>
              <SearchLabel className="text-[#7b7b7b]">
                {t('Repeat')}
              </SearchLabel>
              <SearchLabel>{repeatStr(calendarInfo?.repeatType)}</SearchLabel>
            </SearchItemContainer>
            <SearchItemContainer className={'justify-start'}>
              <SearchLabel className="text-[#7b7b7b]">
                {t('VisibilityStatus')}
              </SearchLabel>
              <SearchLabel>
                {calendarInfo?.publicStatus === 'PARTIAL'
                  ? t('RestrictedP')
                  : t('AllP')}
              </SearchLabel>
            </SearchItemContainer>
            <SearchItemContainer className={'justify-start'}>
              <SearchLabel className="text-[#7b7b7b]">{t('Start')}</SearchLabel>
              <SearchLabel>
                {calendarInfo?.startDate}{' '}
                {String(calendarInfo?.startTime).slice(0, 5)}
              </SearchLabel>
            </SearchItemContainer>
            <SearchItemContainer className={'justify-start'}>
              <SearchLabel className="text-[#7b7b7b]">{t('End')}</SearchLabel>
              <SearchLabel>
                {calendarInfo?.endDate}{' '}
                {String(calendarInfo?.endTime).slice(0, 5)}
              </SearchLabel>
            </SearchItemContainer>
            <SearchItemContainer className={'justify-start'}>
              <SearchLabel className="text-[#7b7b7b]">
                {t('Content')}
              </SearchLabel>
              <SearchHtmlLabel>{calendarInfo?.context}</SearchHtmlLabel>
            </SearchItemContainer>
          </div>
          <SearchItemContainer className={'justify-end pb-10'}>
            <CustomButton
              variant={'secondary'}
              onClick={() => {
                createFleetMutation.mutate(bodyRequest);
              }}
            >
              {t('Delete')}
            </CustomButton>
            <CustomButton
              variant={'first'}
              onClick={() => {
                onClose();
                if (calendarInfo) {
                  openScheduleAddPopup(calendarInfo, () => {
                    console.log('스케줄 추가 완료');
                    onConfirm();
                  });
                } else {
                  console.error('calendarInfo가 없습니다.');
                }
              }}
            >
              {t('Edit')}
            </CustomButton>
          </SearchItemContainer>
        </div>
      </div>
    </Layout>
  );
};

export default ScheduleInfoPopup;
