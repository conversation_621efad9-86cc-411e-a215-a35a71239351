import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/Common/useToast.tsx';
import CustomButton from '@/Common/Components/CustomButton.tsx';
import { Checkbox } from '@/Common/Components/CheckBox.tsx';
import TempSavePopup from '@/Pages/Notice/components/TempSavePopup.tsx';
import SearchItemContainer from '@/Common/Components/SearchItemContainer.tsx';
import SearchLabel from '@/Common/Components/SearchLabel.tsx';
import { RadioGroup, RadioGroupItem } from '@/Common/Radio.tsx';
import DaySelector from '@/Common/Components/DaySelector.tsx';
import TimeSelector from '@/Common/Components/TimeSelector.tsx';
import SearchDropdown from '@/Common/Components/SearchDropDown.tsx';
import Input from '@/Common/Components/Input.tsx';
import { useState, useEffect } from 'react';
import FileDropDown from '@/Common/Components/FileDropDown.tsx';
import useNoticePopup from '@/Pages/Notice/components/useNoticePopup.tsx';
import { Editor } from '@tinymce/tinymce-react';
import { noticeApi } from '@/api';
import {
  AddNoticeLangTypeEnum,
  GetNoticePageNoticeTypeEnum,
  AddNoticeReadPermissionEnum,
} from '@/api/generated/index.ts';
import { NoticeInfo } from '@/api/generated/index.ts';

type ExtendedNoticeInfo = NoticeInfo & {
  topFixed?: boolean;
};
import dayjs from 'dayjs';

const NoticeAdd = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { toast } = useToast();

  const [noticeType, setNoticeType] = useState<
    'SYSTEM' | 'TERMS' | 'UPDATE' | 'NORMAL'
  >('NORMAL');
  const [readPermission, setReadPermission] = useState<'ALL' | 'DEALER'>('ALL');
  const [langType] = useState<'KR' | 'US'>('KR');
  const [topFixed, setTopFixed] = useState<boolean>(false);
  const [files, setFiles] = useState<File[]>([]);
  const [isTempPopupOpen, setIsTempPopupOpen] = useState(false);

  const { openPageOutPopup } = useNoticePopup();
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');

  const [periodType, setPeriodType] = useState('1');

  const [startDate, setStartDate] = useState<string>('');
  const [startTime, setStartTime] = useState<string>('09:00');
  const [endDate, setEndDate] = useState<string>('');
  const [endTime, setEndTime] = useState<string>('18:00');
  const [existingFileNames, setExistingFileNames] = useState<string[]>([]);

  const { noticeId } = useParams();
  const isEdit = !!noticeId;

  const handleFileChange = (newFiles: File[]) => {
    setFiles(newFiles);
  };

  const handleLoadTempItem = (item: NoticeInfo) => {
    setTitle(item.title ?? '');
    setContent(item.context ?? '');
    setNoticeType(
      ['SYSTEM', 'TERMS', 'UPDATE', 'NORMAL'].includes(item.noticeType ?? '')
        ? (item.noticeType as 'SYSTEM' | 'TERMS' | 'UPDATE' | 'NORMAL')
        : 'NORMAL',
    );
  };

  const handleClickTempSavePopup = () => {
    setIsTempPopupOpen(true);
  };

  const noticeTypeOptions = [
    { key: t('SystemCheck'), value: GetNoticePageNoticeTypeEnum.System },
    { key: t('TermsChanges'), value: GetNoticePageNoticeTypeEnum.Terms },
    { key: t('Update'), value: GetNoticePageNoticeTypeEnum.Update },
    { key: t('Normal'), value: GetNoticePageNoticeTypeEnum.Normal },
  ];

  const readPermissionOptions = [
    { key: t('All'), value: AddNoticeReadPermissionEnum.All },
    { key: t('Dealer'), value: AddNoticeReadPermissionEnum.Dealer },
  ];

  const langTypeOptions = [
    { key: t('Korean'), value: AddNoticeLangTypeEnum.Kr },
    { key: t('English'), value: AddNoticeLangTypeEnum.Us },
  ];

  const handleRegister = async () => {
    try {
      if (!title.trim() || !content.trim()) return;

      const hasPeriodData = startDate && startTime && endDate && endTime;

      const formattedStartDate = hasPeriodData
        ? dayjs(`${startDate}`).format('YYYY-MM-DD')
        : undefined;

      const formattedStartTime = hasPeriodData
        ? dayjs(
            `${formattedStartDate} ${startTime}`,
            'YYYY-MM-DD HH:mm',
          ).format('HH:mm:ss')
        : undefined;

      const formattedEndDate = hasPeriodData
        ? dayjs(`${endDate}`).format('YYYY-MM-DD')
        : undefined;

      const formattedEndTime = hasPeriodData
        ? dayjs(`${formattedEndDate} ${endTime}`, 'YYYY-MM-DD HH:mm').format(
            'HH:mm:ss',
          )
        : undefined;

      if (isEdit && noticeId) {
        // 수정
        await noticeApi.modifyNotice({
          noticeId: Number(noticeId),
          noticeType,
          noticePeriodType: periodType === '1' ? 'ALWAYS' : 'SCHEDULED',
          readPermission,
          topFixed,
          langType,
          temporary: false,
          title,
          context: content,
          file: files.length ? files : undefined,
          startDate: periodType === '2' ? formattedStartDate : undefined,
          startTime: periodType === '2' ? formattedStartTime : undefined,
          endDate: periodType === '2' ? formattedEndDate : undefined,
          endTime: periodType === '2' ? formattedEndTime : undefined,
        });
      } else {
        // 등록
        await noticeApi.addNotice({
          noticeType,
          noticePeriodType: periodType === '1' ? 'ALWAYS' : 'SCHEDULED',
          readPermission,
          topFixed,
          langType,
          temporary: false,
          title,
          context: content,
          file: files.length ? files : undefined,
          startDate: periodType === '2' ? formattedStartDate : undefined,
          startTime: periodType === '2' ? formattedStartTime : undefined,
          endDate: periodType === '2' ? formattedEndDate : undefined,
          endTime: periodType === '2' ? formattedEndTime : undefined,
        });
      }

      navigate('/notice');
    } catch (error) {
      console.error('공지 등록 실패:', error);
    }
  };

  const handleBackNavigation = () => {
    openPageOutPopup(() => {
      navigate(-1);
    });
  };

  // 공지사항 수정
  const { data: noticeDetail } = useQuery<NoticeInfo | null>({
    queryKey: ['notice-detail', noticeId],
    queryFn: async () => {
      if (!noticeId) return null;
      const res = await noticeApi.getNoticeDetail({
        noticeId: Number(noticeId),
      });
      return res.data as NoticeInfo;
    },
    enabled: !!noticeId,
  });

  useEffect(() => {
    if (!noticeDetail) return;
    setTitle((noticeDetail as NoticeInfo).title ?? '');
    setContent((noticeDetail as NoticeInfo).context ?? '');
    setNoticeType(
      ['SYSTEM', 'TERMS', 'UPDATE', 'NORMAL'].includes(
        (noticeDetail as NoticeInfo).noticeType ?? '',
      )
        ? ((noticeDetail as NoticeInfo).noticeType as
            | 'SYSTEM'
            | 'TERMS'
            | 'UPDATE'
            | 'NORMAL')
        : 'NORMAL',
    );
    setStartDate((noticeDetail as NoticeInfo).startDate ?? '');
    setStartTime((noticeDetail as NoticeInfo).startTime ?? '');
    setTopFixed((noticeDetail as ExtendedNoticeInfo).topFixed ?? false);
    setEndTime((noticeDetail as NoticeInfo).endTime ?? '');
    setPeriodType(
      (noticeDetail as NoticeInfo).noticePeriodType === 'SCHEDULED' ? '2' : '1',
    );
    setExistingFileNames(noticeDetail.fileName ?? []);
    setTopFixed((noticeDetail as ExtendedNoticeInfo).topFixed ?? false);
  }, [noticeDetail]);

  const handleTemporarySave = async () => {
    try {
      if (!title.trim() && !content.trim()) return;

      const hasPeriodData = startDate && startTime && endDate && endTime;

      const formattedStartDate = hasPeriodData
        ? dayjs(`${startDate}`).format('YYYY-MM-DD')
        : undefined;

      const formattedStartTime = hasPeriodData
        ? dayjs(
            `${formattedStartDate} ${startTime}`,
            'YYYY-MM-DD HH:mm',
          ).format('HH:mm:ss')
        : undefined;

      const formattedEndDate = hasPeriodData
        ? dayjs(`${endDate}`).format('YYYY-MM-DD')
        : undefined;

      const formattedEndTime = hasPeriodData
        ? dayjs(`${formattedEndDate} ${endTime}`, 'YYYY-MM-DD HH:mm').format(
            'HH:mm:ss',
          )
        : undefined;

      await noticeApi.addNotice({
        noticeType,
        noticePeriodType: periodType === '1' ? 'ALWAYS' : 'SCHEDULED',
        readPermission,
        topFixed,
        langType,
        temporary: true,
        title,
        context: content,
        file: files.length ? files : undefined,
        startDate: periodType === '2' ? formattedStartDate : undefined,
        startTime: periodType === '2' ? formattedStartTime : undefined,
        endDate: periodType === '2' ? formattedEndDate : undefined,
        endTime: periodType === '2' ? formattedEndTime : undefined,
      });

      toast({
        types: 'success',
        description: t('Savedtemporarily'),
      });

      // 성공 후 메시지나 상태 변경 필요하면 여기에 추가
      console.log('임시 저장 완료');
    } catch (error) {
      console.error('임시 저장 실패:', error);
    }
  };

  const handleRemoveExistingFile = (fileName: string) => {
    setExistingFileNames((prev) => prev.filter((f) => f !== fileName));
  };

  return (
    <CustomFrame
      name={isEdit ? t('NoticeEdit') : t('NoticeAdd')}
      back={true}
      onBackClick={handleBackNavigation}
    >
      <div className={'px-10 space-y-12 pb-10'}>
        <div className="h-[1px] bg-[#cccccc] relative w-full">
          <div className={'absolute -top-10 right-0 flex gap-2 items-center'}>
            <div className={'mr-4'}>
              <Checkbox
                label={t('PinToTop')}
                checked={topFixed}
                onCheckedChange={(checked) => setTopFixed(!!checked)}
              />
            </div>

            <CustomButton size={'sm'} onClick={handleClickTempSavePopup}>
              {t('TemporaryStorage')}
            </CustomButton>
            <CustomButton
              size={'sm'}
              disabled={!title.trim() && !content.trim()}
              onClick={handleTemporarySave}
            >
              {t('Save')}
            </CustomButton>

            <CustomButton
              size={'sm'}
              disabled={!title.trim() || !content.trim()}
              onClick={handleRegister}
            >
              {t('Register')}
            </CustomButton>
          </div>
        </div>
        <div className={'space-y-5 px-9'}>
          <SearchItemContainer className={'gap-5 justify-start'}>
            <SearchLabel>{t('NoticePeriod')}</SearchLabel>
            <RadioGroup
              className={'text-xl font-bold flex gap-6'}
              value={periodType}
              onValueChange={(value) => setPeriodType(value)}
            >
              <RadioGroupItem value={'1'} label={t('Always')} />
              <RadioGroupItem value={'2'} label={t('Period')} />
            </RadioGroup>
            <SearchItemContainer
              className="gap-3"
              style={{
                opacity: periodType === '1' ? 0.5 : 1,
                pointerEvents: periodType === '1' ? 'none' : 'auto',
              }}
            >
              <SearchLabel>{t('PostingTime')}</SearchLabel>
              <SearchItemContainer
                className="items-center gap-1"
                style={{
                  opacity: periodType === '1' ? 0.5 : 1,
                  pointerEvents: periodType === '1' ? 'none' : 'auto',
                }}
              >
                <DaySelector initValue={startDate} onChange={setStartDate} />
                <TimeSelector initValue={startTime} onChange={setStartTime} />
                <span>~</span>
                <DaySelector initValue={endDate} onChange={setEndDate} />
                <TimeSelector initValue={endTime} onChange={setEndTime} />
              </SearchItemContainer>
            </SearchItemContainer>
          </SearchItemContainer>
          <div className="flex gap-6">
            <SearchItemContainer className={'justify-start'}>
              <SearchLabel>{t('ReadPermission')}</SearchLabel>
              <SearchDropdown
                options={readPermissionOptions}
                placeholder={t('All')}
                selectedKey={readPermission}
                onChange={(value) =>
                  setReadPermission(value as 'ALL' | 'DEALER')
                }
              />
            </SearchItemContainer>
            <SearchItemContainer className={'justify-start'}>
              <SearchLabel>{t('TypeN')}</SearchLabel>
              <SearchDropdown
                options={noticeTypeOptions}
                placeholder={t('SelectNoticeType')}
                selectedKey={noticeType}
                onChange={(value) =>
                  setNoticeType(
                    value as 'SYSTEM' | 'TERMS' | 'UPDATE' | 'NORMAL',
                  )
                }
              />
            </SearchItemContainer>
            <SearchItemContainer className={'justify-start'}>
              <SearchLabel>{t('Language')}</SearchLabel>
              <SearchDropdown
                options={langTypeOptions}
                placeholder={t('All')}
                onChange={() => {}}
              />
            </SearchItemContainer>
          </div>
          <div className="h-px bg-[#cccccc] relative w-full" />
          <div className={'space-y-3'}>
            <SearchItemContainer className={'justify-start gap-5'}>
              <SearchLabel className={'min-w-24'}>
                {t('Title')}
                <span className="text-[#ff0000] text-xl font-bold leading-7">
                  *
                </span>
              </SearchLabel>
              <Input
                placeholder={t('TitleNotice')}
                width={'flex-1'}
                value={title}
                onChange={(e) => setTitle(e.target.value)}
              />
            </SearchItemContainer>
            <SearchItemContainer
              className={'justify-start gap-5 items-start !flex-nowrap'}
            >
              <SearchLabel className={'min-w-24'}>
                {t('Content')}
                <span className="text-[#ff0000] text-xl font-bold leading-7">
                  *
                </span>
              </SearchLabel>
              <Editor
                value={content}
                onEditorChange={(newContent) => setContent(newContent)}
                apiKey="o23x3crldmqswoqnwr5hpx0q4lcc75y4cxei5519iuo7mhbr"
                init={{
                  height: 600,
                  menubar: false,
                  plugins: [
                    'anchor',
                    'autolink',
                    'charmap',
                    'codesample',
                    'link',
                    'lists',
                    'searchreplace',
                    'visualblocks',
                    'wordcount',
                  ],
                  toolbar:
                    'undo redo | blocks fontsize | bold italic underline strikethrough | link mergetags | addcomment showcomments | spellcheckdialog a11ycheck typography | align lineheight | checklist numlist bullist indent outdent',
                  tinycomments_mode: 'embedded',
                  tinycomments_author: 'Author name',
                  ai_request: () => {},
                }}
                initialValue=""
              />
            </SearchItemContainer>
            <SearchItemContainer
              className={'justify-start gap-5 items-start !flex-nowrap'}
            >
              <SearchLabel className={'min-w-24'}>
                {t('AttatchFile')}
              </SearchLabel>
              <div className="w-full flex flex-col flex-1 gap-1">
                <FileDropDown
                  onFilesChange={handleFileChange}
                  existingFileNames={existingFileNames}
                  onRemoveExistingFileName={handleRemoveExistingFile}
                />
                <div className="text-xs font-semibold text-gray-20 leading-[18px]">
                  {t('UpTo5FilesCanBeUploadedJpgJpegBmpPngGif')}
                </div>
              </div>
            </SearchItemContainer>
          </div>
        </div>
        {isTempPopupOpen && (
          <TempSavePopup
            isOpen={isTempPopupOpen}
            onClose={() => setIsTempPopupOpen(false)}
            onLoad={handleLoadTempItem}
          />
        )}
      </div>
    </CustomFrame>
  );
};

export default NoticeAdd;
