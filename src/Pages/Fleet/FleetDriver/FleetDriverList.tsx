import { useTranslation } from 'react-i18next';
import { FormEvent, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import usePopup from '@/hooks/usePopup.tsx';
import UseFleetPopup from '@/Pages/Fleet/Component/UseFleetPopup.tsx';
import { Tabs } from '@radix-ui/themes';
import Dropdown from '@/Common/Components/DropDown.tsx';
import SearchLabel from '@/Common/Components/SearchLabel.tsx';
import SearchDropdown from '@/Common/Components/SearchDropDown.tsx';
import SearchItemContainer from '@/Common/Components/SearchItemContainer.tsx';
import Input from '@/Common/Components/Input.tsx';
import CustomButton from '@/Common/Components/CustomButton.tsx';
import CommonTable from '@/Common/Components/CommonTable.tsx';
import { Tooltip } from '@radix-ui/themes';
import VehiclesIcon from '@/assets/images/svg/vehiclesIcon.tsx';
import UserIcon from '@/assets/images/svg/UserIcon.tsx';
import { driverApi, fleetManagementApi } from '@/api';
import { DriverInfo, type EquipmentInfo, FleetDTO } from '@/api/generated';
import { useQuery } from '@tanstack/react-query';
import { driverListStructure, eqListStructure, fleetSelectItem } from '@/types';

const FleetDriverList = () => {
  const { t } = useTranslation();

  const { openDRegisterDriverPopup, openDDriverInfoPopup } = usePopup();
  const { openFdDeletePopup } = UseFleetPopup();

  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('Driver');

  const [pageNum, setPageNum] = useState(1);
  const [pageSize] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [selectedCheck, setSelectedCheck] = useState<string[]>([]);

  const [riderTotalCount, setRiderTotalCount] = useState(0);
  const [eqTotalCount, setEqTotalCount] = useState(0);

  //fleet/management/list
  // 입력 폼 값을 저장하는 상태
  const [formValues, setFormValues] = useState({
    siteId: 'ALL',
    driverNm: '',
    smodel: '',
    hogi: '',
  });

  // 실제 검색에 사용될 검색 조건을 저장하는 상태
  const [searchParams, setSearchParams] = useState({
    siteId: 'ALL',
    driverNm: '',
    smodel: '',
    hogi: '',
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormValues({
      ...formValues,
      [name]: value,
    });
  };

  const getSearch = () => {
    // 페이지를 1로 리셋
    setPageNum(1);
    // 검색 조건 업데이트
    setSearchParams({ ...formValues });
  };

  const { data: riderListData } = useQuery({
    queryKey: [
      'get-driver-driver-list-page',
      pageNum,
      pageSize,
      searchParams.siteId,
      searchParams.driverNm,
      searchParams.smodel,
      searchParams.hogi,
    ],
    queryFn: async () => {
      const response = await driverApi.getDriverPage({
        pageNum: pageNum,
        pageSize: pageSize,
        siteId: searchParams.siteId,
        driverNm: searchParams.driverNm,
        smodel: searchParams.smodel,
        hogi: searchParams.hogi,
      });

      // Extract total count from the first item if available
      if (
        response.data &&
        response.data.length > 0 &&
        response.data[0].totalCnt
      ) {
        console.log(response.data[0].totalCnt);
        setRiderTotalCount(response.data[0].totalCnt);
        if (activeTab === 'Driver') setTotalCount(response.data[0].totalCnt);
      } else {
        setRiderTotalCount(1);
        setTotalCount(1);
      }

      const riderData: driverListStructure[] = response.data.map(
        (item: DriverInfo) => ({
          driver: item.driverNm ?? '',
          fleet: item.siteNm ?? '',
          classification: item.idTypeNm ?? '',
          startD: item.startDay ?? '',
          expiration: item.endDay ?? '',
          start: item.frHms ?? '',
          end: item.toHms ?? '',
          eq: item.eqpCnt ?? '',
          send: item.sendYn ?? '-',
        }),
      );
      return riderData;
    },
    enabled: activeTab === 'Driver',
  });

  const { data: eqListData } = useQuery({
    queryKey: [
      'get-driver-eq-list-page',
      pageNum,
      pageSize,
      searchParams.siteId,
      searchParams.smodel,
      searchParams.hogi,
    ],
    queryFn: async () => {
      const response = await driverApi.getEquipmentPage({
        pageNum: pageNum,
        pageSize: pageSize,
        siteId: searchParams.siteId,
        smodel: searchParams.smodel,
        hogi: searchParams.hogi,
      });

      // Extract total count from the first item if available
      if (
        response.data &&
        response.data.length > 0 &&
        response.data[0].totalCnt
      ) {
        console.log(response.data[0].totalCnt);
        setEqTotalCount(response.data[0].totalCnt);
        if (activeTab !== 'Driver') setTotalCount(response.data[0].totalCnt);
      } else {
        setEqTotalCount(1);
        setTotalCount(1);
      }

      const eqData: eqListStructure[] = response.data.map(
        (item: EquipmentInfo) => ({
          no: item.rowNumber ?? '',
          fleet: item.siteNm ?? '',
          model: item.smodel ?? '',
          driver: '', //TODO: 장비 번호 추가 필요
          send: item.nsendYn ?? '-',
        }),
      );

      return eqData;
    },
    enabled: activeTab !== 'Driver',
  });

  const handlePageChange = (newPage: number) => {
    console.log(newPage);
    setPageNum(newPage);
  };

  const handleSelectionChange = (
    selectedRows: eqListStructure[] | driverListStructure[],
  ) => {
    if (Array.isArray(selectedRows)) {
      // Check if each row has a seqNo
      setSelectedCheck(selectedRows.map((row) => row.fleet ?? 'default')); // Provide a default if seqNo is missing
    }
  };

  const { data: fleetEquipmentData } = useQuery({
    queryKey: ['get-feet-driver-list'],
    queryFn: async () => {
      const response = await fleetManagementApi.getFleetList({
        searchName: undefined,
      });
      const fleetSelectData: fleetSelectItem[] = [
        { key: '전체', value: 'ALL' },
      ];

      fleetSelectData.push(
        ...response.data.map((item: FleetDTO) => ({
          key: item.name ?? '',
          value: item.seqNo?.toString() ?? 'ALL',
        })),
      );

      return fleetSelectData;
    },
    enabled: true,
  });

  const setSelectedValue = (
    key: string | number | FormEvent<HTMLDivElement>,
  ) => {
    const newValue = key as string;
    const newFormValues = {
      ...formValues,
      siteId: newValue,
    };
    setFormValues(newFormValues);
  };

  const setActiveTabFunction = (tab: string) => {
    setActiveTab(tab);
    setPageNum(1);
    if (tab === 'Driver') {
      setTotalCount(riderTotalCount);
    } else {
      setTotalCount(eqTotalCount);
    }
  };

  const routeDetails = () => {
    navigate('/fleet_driver/fleetDriverDetails');
  };

  const routeCell = ({ cell }: { cell: { getValue: () => unknown } }) => (
    <span onClick={routeDetails} className="cursor-pointer">
      {cell.getValue() as string}
    </span>
  );

  const fleetOptions = [
    { key: 'Fleet1', value: 'fleet1' },
    { key: 'Fleet2', value: 'fleet2' },
    { key: 'Fleet3', value: 'fleet3' },
  ];

  const columns1 = [
    {
      header: t('Driver'),
      accessorKey: 'driver',
      cell: ({ cell }: { cell: { getValue: () => unknown } }) => (
        <span
          onClick={routeDetails}
          className="text-information underline underline-offset-2 cursor-pointer"
        >
          {cell.getValue() as string}
        </span>
      ),
    },
    {
      header: t('Fleet'),
      accessorKey: 'fleet',
      cell: ({ cell }: { cell: { getValue: () => unknown } }) => (
        <Dropdown
          className={'bg-transparent border-0'}
          onChange={() => undefined}
          options={fleetOptions}
          placeholder={cell.getValue() as string}
        ></Dropdown>
      ),
    },
    {
      header: t('IDType'),
      accessorKey: 'classification',
      cell: routeCell,
    },
    {
      header: t('LicenseStartDate'),
      accessorKey: 'startD',
      cell: routeCell,
    },
    {
      header: t('LicenseExpiryDate'),
      accessorKey: 'expiration',
      cell: routeCell,
    },
    {
      header: t('StartTime'),
      accessorKey: 'start',
      cell: routeCell,
    },
    {
      header: t('EndTime'),
      accessorKey: 'end',
      cell: routeCell,
    },
    {
      header: t('OperableEquipment'),
      accessorKey: 'eq',
      cell: routeCell,
    },
    {
      header: t('Send'),
      accessorKey: 'send',
      cell: routeCell,
    },
  ];

  const columns2 = [
    {
      header: t('No'),
      accessorKey: 'no',
    },
    {
      header: t('Fleet'),
      accessorKey: 'fleet',
      cell: ({ cell }: { cell: { getValue: () => unknown } }) => (
        <Dropdown
          className={'bg-transparent border-0'}
          onChange={() => undefined}
          options={fleetOptions}
          placeholder={cell.getValue() as string}
        ></Dropdown>
      ),
    },
    {
      header: t('Model'),
      accessorKey: 'model',
    },
    {
      header: t('MachineID'),
      accessorKey: 'unit',
      cell: ({ cell }: { cell: { getValue: () => unknown } }) => (
        <span className="text-information underline underline-offset-2 cursor-pointer">
          {cell.getValue() as string}
        </span>
      ),
    },
    {
      header: t('Driver'),
      accessorKey: 'driver',
      cell: ({ cell }: { cell: { getValue: () => unknown } }) => (
        <span
          onClick={openDDriverInfoPopup}
          className="text-information underline underline-offset-2 cursor-pointer"
        >
          {cell.getValue() as string}
        </span>
      ),
    },
    {
      header: t('Send'),
      accessorKey: 'send',
    },
  ];

  return (
    <Tabs.Content value={'ManageDriverList'} className={'space-y-10'}>
      {/* 필터 */}
      <article>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-6">
            <SearchItemContainer>
              <SearchLabel>{t('Fleet')}</SearchLabel>
              <SearchDropdown
                onChange={(
                  value: string | number | FormEvent<HTMLDivElement>,
                ) => {
                  console.log('Selected value:', value);
                  setSelectedValue(value);
                }}
                options={fleetEquipmentData || []}
                placeholder={t('All')}
              />
            </SearchItemContainer>
            <div className="flex items-center gap-6">
              <span className="body1-b">{t('Model')}</span>
              <Input
                name="smodel"
                variant="underbar"
                placeholder={t('Model')}
                className="w-[146px]"
                value={formValues.smodel}
                onChange={handleInputChange}
              />
            </div>
            <div className="flex items-center gap-6">
              <span className="body1-b">{t('MachineID')}</span>
              <Input
                name="hogi"
                variant="underbar"
                placeholder={t('MachineID')}
                className="w-[146px]"
                value={formValues.hogi}
                onChange={handleInputChange}
              />
            </div>
            <div className="flex items-center gap-6">
              <span className="body1-b">{t('Driver')}</span>
              <Input
                name="driverNm"
                variant="underbar"
                placeholder={t('Driver')}
                className="w-[146px]"
                value={formValues.driverNm}
                onChange={handleInputChange}
              />
            </div>
          </div>
          <div className="flex items-center gap-3">
            <CustomButton size={'lg'} variant="blue" onClick={getSearch}>
              {t('Search')}
            </CustomButton>
            <CustomButton size={'lg'}>{t('Print')}</CustomButton>
          </div>
        </div>
      </article>

      {/* 테이블 */}
      <article>
        {/* 테이블 버튼 */}
        <div className="mb-3 f flex items-center gap-6">
          {/*  */}
          <div className="trans-btn">
            <Tooltip content={t('ViewByDriver')}>
              <button
                onClick={() => setActiveTabFunction('Driver')}
                className={activeTab === 'Driver' ? 'active' : ''}
              >
                <UserIcon />
              </button>
            </Tooltip>
            <div className="trans-divider"></div>
            <Tooltip content={t('ViewByEquipment')}>
              <button
                onClick={() => setActiveTabFunction('eq')}
                className={activeTab === 'eq' ? 'active' : ''}
              >
                <VehiclesIcon />
              </button>
            </Tooltip>
          </div>

          <div className="flex gap-2">
            <CustomButton
              size={'sm'}
              onClick={openFdDeletePopup}
              disabled={selectedCheck.length === 0}
            >
              {t('Delete')}
            </CustomButton>
            <CustomButton size={'sm'} onClick={openDRegisterDriverPopup}>
              {t('AddD')}
            </CustomButton>
            <CustomButton size={'sm'} disabled={selectedCheck.length === 0}>
              {t('AssignS')}
            </CustomButton>
          </div>
        </div>

        {(() => {
          const tableProps =
            activeTab === 'Driver'
              ? {
                  columns: columns1,
                  data: riderListData ?? [], // 수정
                }
              : {
                  columns: columns2,
                  data: eqListData ?? [], // 수정
                };

          return (
            <CommonTable
              columns={tableProps.columns}
              data={tableProps.data || []}
              isPagination={true}
              isCheckbox={true}
              customPageSize={pageSize}
              currentPage={pageNum}
              totalCount={totalCount}
              onPageChange={handlePageChange}
              onSelectionChange={handleSelectionChange} // 중요! 함수를 prop으로 전달
            />
          );
        })()}
      </article>
    </Tabs.Content>
  );
};
export default FleetDriverList;
