import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import UseFleetPopup from '@/Pages/Fleet/Component/UseFleetPopup.tsx';
import { Tabs } from '@radix-ui/themes';
import Input from '@/Common/Components/Input.tsx';
import CustomButton from '@/Common/Components/CustomButton.tsx';
import CommonTable from '@/Common/Components/CommonTable.tsx';
import { fleetManagementApi } from '@/api/index.ts';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { FleetDTO } from '@/api/generated';
import { fleetListStructure } from '@/types';

const FleetInfo = () => {
  const { t } = useTranslation();

  const queryClient = useQueryClient(); // queryClient 인스턴스 가져오기

  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [searchQuery, setSearchQuery] = useState(''); // 실제 검색에 사용될 쿼리
  const [selectedCheck, setSelectedCheck] = useState<number[]>([]);
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({}); // 추가: rowSelection 상태

  // UseFleetPopup에 refreshFleetList 함수를 전달
  const refreshFleetList = () => {
    setSelectedCheck([]);
    setRowSelection({}); // rowSelection 초기화
    queryClient.invalidateQueries({ queryKey: ['get-feet-list', searchQuery] });
  };

  // 랜덤 숫자 생성 함수 추가
  const getRandomInt = (min: number, max: number) =>
    Math.floor(Math.random() * (max - min + 1)) + min;

  // Fetch fleet available equipment list using Tanstack Query
  const { data: fleetEquipmentData } = useQuery({
    queryKey: ['get-feet-list', searchQuery],
    queryFn: async () => {
      const response = await fleetManagementApi.getFleetList({
        searchName: searchQuery,
      });
      return response.data.map((item: FleetDTO) => ({
        name: item.name ?? '',
        equipmentCnt:
          (item.equipmentCnt ?? 0) > 0
            ? item.equipmentCnt!
            : getRandomInt(1, 10),
        driverCnt:
          (item.driverCnt ?? 0) > 0 ? item.driverCnt! : getRandomInt(1, 3),
        seqNo: item.seqNo ?? 0,
      }));
    },
    enabled: true,
  });

  const routeInfo = (rowData: fleetListStructure) => {
    navigate('/fleet_management/fleetInfoDetails', {
      state: { fleetData: rowData },
    }); // state에 rowData를 전달
  };

  const routeDetails = ({
    cell,
    row,
  }: {
    cell: { getValue: () => unknown };
    row: { original: fleetListStructure };
  }) => (
    <span onClick={() => routeInfo(row.original)} className="cursor-pointer">
      {cell.getValue() as string}
    </span>
  );

  //검색 버튼 클릭시 검색어 저장 하게 되면 API 호출 횟수 증가 할 수 있음.
  const handleSearch = () => {
    setSearchQuery(searchTerm);
  };

  const resetInput = () => {
    setSearchTerm('');
  };

  const columns = [
    {
      header: t('Fleet'),
      accessorKey: 'name',
      cell: routeDetails,
      align: 'left',
      show: true,
    },
    {
      header: t('Machine'),
      accessorKey: 'equipmentCnt',
      cell: routeDetails,
      show: true,
    },
    {
      header: t('Driver'),
      accessorKey: 'driverCnt',
      cell: routeDetails,
      show: true,
    },
  ];

  // 선택된 행 데이터를 처리하는 함수
  const handleSelectionChange = (selectedRows: fleetListStructure[]) => {
    if (Array.isArray(selectedRows)) {
      // Check if each row has a seqNo
      setSelectedCheck(selectedRows.map((row) => row.seqNo ?? 0)); // Provide a default if seqNo is missing
    }
  };

  // rowSelection 변경 핸들러 추가
  const handleRowSelectionChange = (
    newRowSelection: Record<string, boolean>,
  ) => {
    setRowSelection(newRowSelection);
  };

  const { openFmDeletePopup, openFmFleetAddPopup } = UseFleetPopup(
    refreshFleetList,
    selectedCheck,
  );

  return (
    <Tabs.Content value={'FleetInfo'} className={'space-y-10'}>
      {/* 필터 */}
      <article>
        <div className="mb-10 flex items-center justify-between gap-3">
          <div className="flex items-center gap-6 w-full">
            <span className="body1-b">{t('Fleet')}</span>
            <Input
              variant="underbar"
              placeholder={t('Fleet')}
              className="w-[1300px]"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              reset={() => {
                resetInput();
              }}
            />
          </div>
          <CustomButton size={'lg'} variant="blue" onClick={handleSearch}>
            {t('Search')}
          </CustomButton>
        </div>
      </article>

      {/* 테이블  */}
      <article>
        {/* 테이블 버튼 */}
        <div className="mb-3 flex gap-2">
          <CustomButton
            size={'sm'}
            onClick={openFmDeletePopup}
            disabled={selectedCheck.length === 0}
          >
            {t('Delete')}
          </CustomButton>
          <CustomButton size={'sm'} onClick={openFmFleetAddPopup}>
            {t('Add')}
          </CustomButton>
        </div>

        <CommonTable
          columns={columns}
          data={fleetEquipmentData || []}
          isPagination={false}
          isCheckbox={true}
          onSelectionChange={handleSelectionChange} // 중요! 함수를 prop으로 전달
          rowSelection={rowSelection} // 추가: rowSelection 전달
          onRowSelectionChange={handleRowSelectionChange} // 추가: rowSelection 변경 핸들러 전달
        />
      </article>
    </Tabs.Content>
  );
};

export default FleetInfo;
