import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/Common/useToast.tsx';
import UseFAQPopup from '@/Pages/FAQ/Component/UseFAQPopup.tsx';
import TempSavePopup from '@/Pages/FAQ/Component/TempSavePopup.tsx';
import { Checkbox } from '@/Common/Components/CheckBox.tsx';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import CustomButton from '@/Common/Components/CustomButton.tsx';
import SearchLabel from '@/Common/Components/SearchLabel.tsx';
import SearchDropdown from '@/Common/Components/SearchDropDown.tsx';
import SearchItemContainer from '@/Common/Components/SearchItemContainer.tsx';
import { Editor } from '@tinymce/tinymce-react';
import FileDropDown from '@/Common/Components/FileDropDown.tsx';
import { faqApi } from '@/api';
import {
  AddFaqFaqTypeEnum,
  AddFaqReadPermissionEnum,
  AddFaqLangTypeEnum,
  ModifyFaqFaqTypeEnum,
  ModifyFaqReadPermissionEnum,
  ModifyFaqLangTypeEnum,
} from '@/api/generated/index.ts';
import { FaqInfo } from '@/api/generated/src/api/generated/models/faq-info';
import { GetFaqPageFaqTypeEnum } from '@/api/generated/src/api/generated/api/faq-api';
import { useLocation } from 'react-router-dom';

const RegistrationFAQ = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { toast } = useToast();

  const { openFaqOutPopup } = UseFAQPopup();

  // 상태 관리
  const [faqType, setFaqType] = useState<string>('ALL');
  const [readPermission, setReadPermission] = useState<string>('ALL');
  const [langType, setLangType] = useState<string>('KR');
  const [question, setQuestion] = useState<string>('');
  const [content, setContent] = useState<string>('');
  const [topFixed, setTopFixed] = useState<boolean>(false);
  const [uploadFiles, setUploadFiles] = useState<File[]>([]);
  const [existingFileNames, setExistingFileNames] = useState<string[]>([]);

  // FAQ 수정 시, 기존 데이터 불러오기
  const location = useLocation();
  const faqId = location.state?.faqId;
  const isEditMode = !!faqId; // 수정 모드 여부
  const faqItem = location.state?.faqItem;

  useEffect(() => {
    if (faqItem) {
      setExistingFileNames(faqItem.fileName ?? []);
    }
  }, [faqItem]);

  useEffect(() => {
    if (!faqItem) return;

    setFaqType(faqItem.type ?? '');
    setReadPermission(faqItem.readPermission ?? '');
    setLangType(faqItem.langType ?? 'KR');
    setQuestion(faqItem.question ?? '');
    setContent(faqItem.answer ?? '');
    setTopFixed(faqItem.topFixed ?? false);
  }, [faqItem]);

  const [faqTypeOptions] = useState([
    { key: t('All'), value: 'ALL' },
    { key: t('HowToUse'), value: GetFaqPageFaqTypeEnum.Usage },
    { key: t('Account'), value: GetFaqPageFaqTypeEnum.Account },
    { key: t('Others'), value: GetFaqPageFaqTypeEnum.Other },
  ]);

  // 드롭다운 옵션: 열람 권한
  const [readPermissionOptions] = useState([
    { key: t('SuperAdministrator'), value: 'MASTER' },
    { key: t('DelearAdministrator'), value: 'DEALER' },
    { key: t('RepairAdministrator'), value: 'REPAIR' },
    { key: t('MMXAdministrator'), value: 'MMX' },
    { key: t('Driver'), value: 'DRIVER' },
  ]);

  // 드롭다운 옵션: 언어
  const [langTypeOptions] = useState([
    { key: t('Korean'), value: 'KR' },
    { key: t('English'), value: 'US' },
  ]);

  const handleDropdownChange = (field: string, value: string) => {
    switch (field) {
      case 'faqType':
        setFaqType(value);
        break;
      case 'readPermission':
        setReadPermission(value);
        break;
      case 'langType':
        setLangType(value);
        break;
      default:
        break;
    }
  };

  // FAQ 등록 및 수정
  const handleRegisterFaq = async () => {
    try {
      if (faqId) {
        await faqApi.modifyFaq({
          faqId: Number(faqId),
          faqType: faqType as ModifyFaqFaqTypeEnum,
          readPermission: readPermission as ModifyFaqReadPermissionEnum,
          topFixed: topFixed,
          temporary: false,
          question: question,
          answer: content,
          langType: langType as ModifyFaqLangTypeEnum,
          file: uploadFiles,
        });
      } else {
        await faqApi.addFaq({
          faqType: faqType as AddFaqFaqTypeEnum,
          readPermission: readPermission as AddFaqReadPermissionEnum,
          topFixed: topFixed,
          temporary: false,
          question: question,
          answer: content,
          langType: langType as AddFaqLangTypeEnum,
          file: uploadFiles,
        });
      }

      navigate('/faq');
    } catch (error) {
      console.error('등록/수정 실패:', error);
    }
  };

  const handleTemporarySave = async () => {
    try {
      await faqApi.addFaq({
        faqType: faqType as AddFaqFaqTypeEnum,
        readPermission: readPermission as AddFaqReadPermissionEnum,
        topFixed: false,
        temporary: true,
        question: question,
        answer: content,
        langType: langType as AddFaqLangTypeEnum,
        file: uploadFiles,
      });

      toast({
        types: 'success',
        description: t('Savedtemporarily'),
      });
    } catch (error) {
      console.error('임시 저장 실패:', error);
    }
  };

  const [isTempPopupOpen, setIsTempPopupOpen] = useState(false);

  const handleRemoveExistingFileName = (fileName: string) => {
    setExistingFileNames((prev) => prev.filter((f) => f !== fileName));
  };

  const handleLoadTempItem = (item: FaqInfo) => {
    setFaqType(item.faqType ?? 'ALL');
    setReadPermission(item.readPermission ?? 'ALL');
    setLangType(item.langType ?? 'KR');
    setQuestion(item.question ?? '');
    setContent(item.answer ?? '');
  };

  const handleBackNavigation = () => {
    openFaqOutPopup(() => {
      navigate(-1);
    });
  };

  return (
    <CustomFrame
      name={isEditMode ? t('FAQModify') : t('FAQAdd')}
      back={true}
      onBackClick={handleBackNavigation}
    >
      <section className="px-[76px] pb-10 ">
        <div className="divider w-[calc(100%+76px)] mt-0 mx-[-36px]"></div>

        {/* Q&A 버튼 */}
        <article className="relative w-full">
          <div className="absolute -top-[90px] -right-9 flex gap-2 items-center [&_label]:pr-7">
            <Checkbox
              label={t('PinToTop')}
              checked={topFixed}
              onCheckedChange={(checked) => setTopFixed(!!checked)}
            />
            <CustomButton size={'sm'} onClick={() => setIsTempPopupOpen(true)}>
              {t('TemporaryStorage2')}
            </CustomButton>
            <CustomButton
              size="sm"
              disabled={!question.trim() && !content.trim()} // 질문과 답변 중 하나라도 입력되면 활성화
              onClick={handleTemporarySave}
            >
              {t('Save')}
            </CustomButton>
            <CustomButton
              size={'sm'}
              disabled={!question.trim() || !content.trim()} // 질문과 답변 모두 입력되어야 활성화
              onClick={handleRegisterFaq} // 등록 버튼 클릭 시 FAQ 등록
            >
              {t('Register')}
            </CustomButton>
          </div>
        </article>

        {/* 필터 */}
        <article className="flex items-center justify-start gap-6">
          <SearchItemContainer>
            <SearchLabel>{t('QType')}</SearchLabel>
            <SearchDropdown
              selectedKey={faqType}
              onChange={(value) =>
                handleDropdownChange('faqType', value.toString())
              }
              options={faqTypeOptions}
              placeholder={t('HowToUse')}
            />
          </SearchItemContainer>
          <SearchItemContainer>
            <SearchLabel>{t('ViewRead')}</SearchLabel>
            <SearchDropdown
              selectedKey={readPermission}
              onChange={(value) =>
                handleDropdownChange('readPermission', value.toString())
              }
              options={readPermissionOptions}
              placeholder={t('All')}
            />
          </SearchItemContainer>
          <SearchItemContainer>
            <SearchLabel>{t('Language')}</SearchLabel>
            <SearchDropdown
              selectedKey={langType}
              onChange={(value) =>
                handleDropdownChange('langType', value.toString())
              }
              options={langTypeOptions}
              placeholder={t('Korean')}
            />
          </SearchItemContainer>
        </article>

        <div className="divider my-6"></div>

        {/* 문의 입력 */}
        <article className="space-y-3">
          <SearchItemContainer className="mt-5 items-start gap-5">
            <SearchLabel className="min-w-24">
              Q<span className="text-error ml-1">*</span>
            </SearchLabel>
            <textarea
              placeholder={t('ContentFAQ')}
              className="body3-m w-full min-h-[134px] py-3 px-4 border border-gray-40 rounded"
              value={question}
              onChange={(e) => setQuestion(e.target.value)}
            />
          </SearchItemContainer>
          <SearchItemContainer className="items-start gap-5">
            <SearchLabel className="min-w-24">
              A<span className="text-error ml-1">*</span>
            </SearchLabel>
            <Editor
              value={content}
              onEditorChange={(newContent) => setContent(newContent)}
              apiKey="o23x3crldmqswoqnwr5hpx0q4lcc75y4cxei5519iuo7mhbr"
              init={{
                height: 600,
                menubar: false,
                plugins: [
                  'anchor',
                  'autolink',
                  'charmap',
                  'codesample',
                  'link',
                  'lists',
                  'searchreplace',
                  'visualblocks',
                  'wordcount',
                ],
                toolbar:
                  'undo redo | blocks fontsize | bold italic underline strikethrough | link mergetags | addcomment showcomments | spellcheckdialog a11ycheck typography | align lineheight | checklist numlist bullist indent outdent',
                tinycomments_mode: 'embedded',
                tinycomments_author: 'Author name',
                ai_request: () => {},
              }}
            />
          </SearchItemContainer>
          <SearchItemContainer className="items-start gap-5 ">
            <SearchLabel className="min-w-24">{t('AttatchFile')}</SearchLabel>
            <div className="w-full flex flex-col gap-1">
              <FileDropDown
                onFilesChange={setUploadFiles}
                existingFileNames={existingFileNames}
                onRemoveExistingFileName={handleRemoveExistingFileName}
              />
              <div className="text-xs font-semibold text-gray-20 leading-[18px]">
                {t('UpTo5FilesCanBeUploadedJpgJpegBmpPngGif')}
              </div>
            </div>
          </SearchItemContainer>
        </article>

        {isTempPopupOpen && (
          <TempSavePopup
            isOpen={isTempPopupOpen}
            onClose={() => setIsTempPopupOpen(false)}
            onLoad={handleLoadTempItem}
          />
        )}
      </section>
    </CustomFrame>
  );
};

export default RegistrationFAQ;
