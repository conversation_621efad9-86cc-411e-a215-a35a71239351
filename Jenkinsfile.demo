
pipeline {
    agent any

    environment {
        DOCKER_REGISTRY = "harbor.logisteq.com"
        DOCKER_CREDENTIALS_ID = "harbor-login"
        BUILD_TAG = new Date().format('yyyyMMdd-HHmmss')
        GIT_CREDENTIALS_ID = "gitlab-login"        
        
        IMAGE_NAME = "himate/demo/himate-web"        
        GIT_REPOSITORY = "gitlab.logisteq.com/himate/himate-web.git"
        GIT_BRANCH = "carta-web"

        DEPLOY_GIT_REPOSITORY = "gitlab.logisteq.com/infra/dev-k8s-cfgstore.git"
        DEPLOY_GIT_BRANCH = "main"
        FILE_PATH = "basic-helm-chart/himate-demo-values.yaml"

        PREFIX_HTTPS = "https://"
    }

    tools {
        jdk 'jdk-17'
    }

    stages {
        stage('Git Clone') {
            steps {
                git branch: "${GIT_BRANCH}", credentialsId: "${GIT_CREDENTIALS_ID}", url: "${PREFIX_HTTPS}${GIT_REPOSITORY}"
            }
        }

        stage('Build Image') {
            steps {
                script {         
                    sh 'cp .env.dev .env'        
                    def customImage = docker.build("${IMAGE_NAME}", "--build-arg NODE_ENV=dev ." )                    
                    customImage.tag("latest")
                    customImage.tag("${BUILD_TAG}")
                }
            }
        }

        stage('Push Image') {
            steps {
                script {
                  // Login to Harbor registry
                  docker.withRegistry("${PREFIX_HTTPS}${DOCKER_REGISTRY}", "${DOCKER_CREDENTIALS_ID}") {                        
                        docker.image("${IMAGE_NAME}").push("${BUILD_TAG}")
                        docker.image("${IMAGE_NAME}").push("latest")
                    }
                }
            }
        }


        stage('Update Deploy Repository') {
            steps {
                script {
                    // 배포 저장소 클론
                    dir('deploy-repo') {
                        git branch: "${DEPLOY_GIT_BRANCH}", 
                            credentialsId: "${GIT_CREDENTIALS_ID}", 
                            url: "${PREFIX_HTTPS}${DEPLOY_GIT_REPOSITORY}"
                        
                        // Git 사용자 설정 및 sed 명령 실행 후 git push
                        withCredentials([usernamePassword(
                            credentialsId: "${GIT_CREDENTIALS_ID}",
                            usernameVariable: 'GIT_USERNAME',
                            passwordVariable: 'GIT_PASSWORD'
                        )]) {
                            def escapedImageName = IMAGE_NAME.replaceAll('/', '\\\\/')
                            sh """
                                git config user.email '<EMAIL>'
                                git config user.name 'jenkins'
                                git config pull.rebase false                  
                                
                                # 이미지 태그 업데이트
                                #sed -i "/^[[:space:]]*image:.*${escapedImageName}/s|:[0-9]\\{1,\\}[_-][0-9]\\{1,\\}|:${BUILD_TAG}|" ${FILE_PATH}

                                groovy -e "
                                    def file = new File('${FILE_PATH}')
                                    def content = file.text
                                    def newContent = content.replaceAll(/tag: \".*\"/, 'tag: \"${BUILD_TAG}\"')
                                    file.write(newContent)
                                "
                                 
                                git add .
                                git commit -m "Update image tag to ${IMAGE_NAME}:${BUILD_TAG}"
                                
                                # 자격 증명을 URL에 포함시켜 인증
                                git push ${PREFIX_HTTPS}${GIT_USERNAME}:${GIT_PASSWORD}@${DEPLOY_GIT_REPOSITORY} ${DEPLOY_GIT_BRANCH}
                            """
                        }
                    }
                }
            }
        }

        stage('Complete') {
            steps {
                script {
                 sh "echo 'The end'"
                }
            }
      }
    }
}